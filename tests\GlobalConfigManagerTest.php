<?php

namespace FDMC\FileServiceClient\Tests;

use FDMC\FileServiceClient\GlobalConfigManager;
use FDMC\FileServiceClient\FileServiceException;
use PHPUnit\Framework\TestCase;

/**
 * GlobalConfigManager 測試
 */
class GlobalConfigManagerTest extends TestCase
{
    protected function setUp(): void
    {
        // 模擬 env 函數
        $this->mockEnvFunction();
        
        // 重置環境偵測和運行時配置
        GlobalConfigManager::resetEnvironmentDetection();
        GlobalConfigManager::clearRuntimeConfig();
    }
    
    /**
     * 模擬 Laravel 的 env() 函數
     */
    private function mockEnvFunction(): void
    {
        if (!function_exists('env')) {
            eval('
                function env($key, $default = null) {
                    $value = $_ENV[$key] ?? getenv($key);
                    if ($value === false) {
                        return $default;
                    }
                    return $value;
                }
            ');
        }
    }

    protected function tearDown(): void
    {
        // 清理運行時配置
        GlobalConfigManager::clearRuntimeConfig();
    }

    /**
     * 測試環境偵測功能
     */
    public function testEnvironmentDetection(): void
    {
        // 在測試環境中，應該偵測為非 Laravel 環境
        $this->assertFalse(GlobalConfigManager::isLaravel());
    }

    /**
     * 測試配置載入
     */
    public function testConfigLoading(): void
    {
        $config = GlobalConfigManager::config();
        
        $this->assertIsArray($config);
        $this->assertArrayHasKey('baseUrl', $config);
        $this->assertArrayHasKey('http', $config);
        $this->assertArrayHasKey('upload', $config);
    }

    /**
     * 測試點號語法配置存取
     */
    public function testDotNotationAccess(): void
    {
        // 測試獲取巢狀配置
        $timeout = GlobalConfigManager::config('http.timeout');
        $this->assertIsInt($timeout);
        
        // 測試預設值
        $nonExistent = GlobalConfigManager::config('non.existent.key', 'default');
        $this->assertEquals('default', $nonExistent);
    }

    /**
     * 測試動態配置設定
     */
    public function testDynamicConfigSetting(): void
    {
        // 設定單一配置
        GlobalConfigManager::set('test.key', 'test_value');
        $this->assertEquals('test_value', GlobalConfigManager::config('test.key'));
        
        // 批量設定配置
        GlobalConfigManager::set([
            'batch.key1' => 'value1',
            'batch.key2' => 'value2'
        ]);
        
        $this->assertEquals('value1', GlobalConfigManager::config('batch.key1'));
        $this->assertEquals('value2', GlobalConfigManager::config('batch.key2'));
    }

    /**
     * 測試配置移除
     */
    public function testConfigForget(): void
    {
        // 設定配置
        GlobalConfigManager::set('temp.key', 'temp_value');
        $this->assertEquals('temp_value', GlobalConfigManager::config('temp.key'));
        
        // 移除配置
        GlobalConfigManager::forget('temp.key');
        $this->assertNull(GlobalConfigManager::config('temp.key'));
    }

    /**
     * 測試運行時配置優先級
     */
    public function testRuntimeConfigPriority(): void
    {
        // 獲取原始配置值
        $originalTimeout = GlobalConfigManager::config('http.timeout');
        
        // 動態修改配置
        GlobalConfigManager::set('http.timeout', 999);
        
        // 驗證動態配置優先
        $this->assertEquals(999, GlobalConfigManager::config('http.timeout'));
        
        // 清除運行時配置
        GlobalConfigManager::clearRuntimeConfig();
        
        // 驗證恢復原始配置
        $this->assertEquals($originalTimeout, GlobalConfigManager::config('http.timeout'));
    }

    /**
     * 測試獲取所有配置
     */
    public function testGetAllConfig(): void
    {
        $allConfig = GlobalConfigManager::all();
        
        $this->assertIsArray($allConfig);
        $this->assertArrayHasKey('baseUrl', $allConfig);
        $this->assertArrayHasKey('http', $allConfig);
    }

    /**
     * 測試巢狀配置設定
     */
    public function testNestedConfigSetting(): void
    {
        // 設定深層巢狀配置
        GlobalConfigManager::set('deep.nested.config.key', 'deep_value');
        
        $this->assertEquals('deep_value', GlobalConfigManager::config('deep.nested.config.key'));
        
        // 驗證中間層級也被建立
        $this->assertIsArray(GlobalConfigManager::config('deep.nested.config'));
        $this->assertIsArray(GlobalConfigManager::config('deep.nested'));
        $this->assertIsArray(GlobalConfigManager::config('deep'));
    }

    /**
     * 測試配置合併
     */
    public function testConfigMerging(): void
    {
        // 設定部分 HTTP 配置
        GlobalConfigManager::set('http.custom_timeout', 123);
        
        $httpConfig = GlobalConfigManager::config('http');
        
        // 驗證新配置被加入
        $this->assertEquals(123, $httpConfig['custom_timeout']);
        
        // 驗證原有配置仍然存在
        $this->assertArrayHasKey('timeout', $httpConfig);
        $this->assertArrayHasKey('connect_timeout', $httpConfig);
    }
}