{"version": 1, "defects": {"FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testLoadFromNonExistentFileUsesDefaults": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testFindProjectRoot": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testGetConfigSearchPaths": 4, "Tests\\FileServiceClientDownloadTest::testGetDownloadInfoSuccess": 4, "Tests\\FileServiceClientDownloadTest::testGetDownloadInfoMissingData": 4, "Tests\\FileServiceClientDownloadTest::testDownloadObjectApLocal": 4, "Tests\\FileServiceClientDownloadTest::testDownloadObjectRemote": 4, "Tests\\FileServiceClientDownloadTest::testDownloadObjectApLocalFileNotExists": 4, "Tests\\FileServiceClientDownloadTest::testDownloadObjectInvalidObjectId": 4, "Tests\\FileServiceClientDownloadTest::testDownloadObjectInvalidUserId": 4, "Tests\\FileServiceClientDownloadTest::testDownloadObjectPathTraversalAttack": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDetermineStorageTypeWithExplicitType": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDetermineStorageTypeWithStorageObject": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDetermineStorageTypeWithStoragePath": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDetermineStorageTypeDefault": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDownloadFromApLocalWithMissingPath": 3, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDownloadFromApLocalWithNonExistentFile": 3, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDownloadFromApLocalSuccess": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testConfigLoading": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testDotNotationAccess": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testDynamicConfigSetting": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testConfigForget": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testRuntimeConfigPriority": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testGetAllConfig": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testNestedConfigSetting": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testConfigMerging": 4, "FDMC\\FileServiceClient\\Tests\\StorageManagerTest::testStorageManagerInitialization": 3, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testProcessEnvVarsHandlesTypeConversionErrors": 4, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateOutOfRangeValues": 4, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateInvalidEnumValue": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testFileNotReadableException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testFileNotFoundException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testFormatInvalidException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testPermissionDeniedException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testValidationFailedException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testTypeConversionFailedException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testRequiredMissingException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testValueOutOfRangeException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testInvalidEnumValueException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testInvalidUrlException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testInvalidArrayException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testSyntaxErrorException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testInvalidReturnTypeException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testMergeConflictException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testCircularDependencyException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testGetDetailedReport": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testBasicConfigGet": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testDefaultValueTypes": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testFunctionArgumentCount": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigForget": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigHas": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testDotNotationAccess": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testRegisterServices": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testPublishesConfiguration": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testCanRegisterGlobalFunctionWhenConfigNotExists": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testCannotRegisterGlobalFunctionWhenLaravelConfigExists": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testRegisterGlobalConfigFunction": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testLoggingMethods": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testProvidesServices": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testGetDefaultConfigPath": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testShouldRegisterGlobalFunction": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testLoggingWithoutLogService": 4, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testConfigFileExists": 4, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testConfigFileStructure": 4, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testEnvironmentVariableProcessing": 4, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testDefaultValues": 4, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testGlobalFunctionBasicUsage": 4, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testConfigurationSetting": 4, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testConfigurationForget": 4, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testConfigurationHas": 4, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testGlobalConfigManagerIntegration": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testGetAllConfig": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigSet": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testBatchConfigSet": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigSetFunction": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigAll": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigOverride": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testComplexConfigMerge": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testDefaultValueHandling": 4, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishToNative": 4, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishWithForceOverwrite": 4, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishWithoutOverwriteInNonInteractiveEnvironment": 4, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishWithUserConfirmation": 4, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testCreateConfigDirectory": 4, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testGetPublishStatus": 4, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishToInvalidPath": 3, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishConfigFile": 4, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testGetPublishStatusWithAutoDetection": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseIntegerThrowsExceptionForNegativeValue": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseArrayThrowsExceptionForInvalidJson": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseArrayThrowsExceptionForNonArrayJson": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testConfigurationPriority": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testNestedConfigMerging": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testArrayConfigMerging": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testEnvironmentVariableTypeConversion": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testBatchConfigurationSetting": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testConfigurationRemoval": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testConfigurationReset": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testComplexConfigMergeScenario": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testInvalidEnvironmentVariableHandling": 4, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testFileServiceClientWithDefaultConfig": 4, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testFileServiceClientWithCustomOptions": 4, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testStorageManagerWithDefaultConfig": 4, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testStorageManagerWithCustomOptions": 4, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testConfigMerging": 4, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testEnvironmentVariableOverride": 4, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testUserOptionsOverrideEnvironmentVariables": 4, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testBackwardCompatibility": 4, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testLoadDefaultConfig": 4, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testGetConfigValue": 4, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testMergeWithUserOptions": 4, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testEmptyUserOptions": 4, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testGetHttpClientConfig": 4, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testGetUploadConfig": 4, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testGetDownloadConfig": 4, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testConfigCaching": 4, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testNestedArrayMerge": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testCanRegisterGlobalFunctionLogic": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testShouldRegisterGlobalFunctionConditions": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testLoadFromValidConfigFile": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigFileValid": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigFileNotExists": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigFileNotFile": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigFileTooLarge": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigValid": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigNotArray": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigMissingRequired": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigInvalidUrl": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigNumericRange": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigInvalidEnum": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testMergeWithDefaults": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testSingletonInstance": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testSetMinLevel": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogLevels": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogWithContext": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogConfigLoad": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogConfigValidation": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogTypeConversion": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogTypeConversionFailure": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testAddHandler": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testClearHandlers": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogRecord": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testGetLevelName": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testGetLevelFromName": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testMinLevelFiltering": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testMultipleHandlers": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testMergeRecursive": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testMergeConfigs": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testMergeUserOptions": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testApplyEnvironmentOverrides": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testMergeAllConfigs": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testSmartArrayMerge": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testIsIndexedArray": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testDeepCopy": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testValidateMergedConfig": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testHasNestedKey": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testGetConfigDiff": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testEmptyConfigHandling": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testInvalidConfigHandling": 4, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testCreateExistingDirectory": 4, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testCreateDirectoryWithInvalidPath": 4, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishToLaravelWithoutLaravelEnvironment": 3, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateCompleteValidConfig": 4, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateInvalidBaseUrl": 4, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateMissingRequiredField": 4, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateInvalidTypeValues": 4, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateEmptyArray": 4, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testGetValidationRules": 4, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidationResultMethods": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testAutoLogging": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testLogException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testGetRepairSuggestions": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testGetErrorDescription": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testProcessEnvVarsOverridesConfig": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testProcessEnvVarsUsesDefaultWhenEnvNotSet": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseBooleanCorrectly": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseBooleanThrowsExceptionForInvalidValue": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseIntegerCorrectly": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseIntegerThrowsExceptionForInvalidValue": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseFloatCorrectly": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseFloatThrowsExceptionForInvalidValue": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseArrayCorrectly": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseStringCorrectly": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testGetExpectedType": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testProcessEnvironmentVariable": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testProcessEnvironmentVariableHandlesTypeConversionError": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testCompleteEnvironmentProcessing": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testClientInitialization": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testBuildObjectData": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testBuildObjectDataWithAllParameters": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testSetHeaders": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDownloadObjectWithInvalidObjectId": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDownloadObjectWithInvalidUserId": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testInvalidKeyType": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testEmptyKey": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testInvalidKeyFormat": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testInvalidKeyCharacters": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testKeyStartsOrEndsWithDot": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testMissingValueParameter": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testEmptyConfigArray": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigArrayWithNonStringKey": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testEnvironmentDetection": 4, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testGlobalFunctionsLoaded": 4, "FDMC\\FileServiceClient\\Tests\\StorageManagerTest::testBuildStorageData": 4, "FDMC\\FileServiceClient\\Tests\\StorageManagerTest::testBuildStorageDataWithAllParameters": 4, "FDMC\\FileServiceClient\\Tests\\StorageManagerTest::testSetHeaders": 4, "FDMC\\FileServiceClient\\Tests\\StorageManagerTest::testStorageDataTypes": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testBooleanConversionFailedException": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testIntegerConversionFailedException": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testFloatConversionFailedException": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testArrayConversionFailedException": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testStringConversionFailedException": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testUnsupportedTypeException": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testGetErrorDescription": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testGetRepairSuggestions": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testGetDetailedReport": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testConstructorParameters": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testInheritanceFromConfigurationException": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testComplexValueHandling": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testNullValueHandling": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testBooleanValueHandling": 4}, "times": {"FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testEnvironmentDetection": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testConfigLoading": 1.215, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testDotNotationAccess": 1.964, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testDynamicConfigSetting": 2.803, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testConfigForget": 1.583, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testRuntimeConfigPriority": 2.667, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testGetAllConfig": 0.842, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testNestedConfigSetting": 2.899, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testConfigMerging": 0.671, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testLoadFromValidConfigFile": 0.648, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testLoadFromNonExistentFileUsesDefaults": 0.003, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testFindProjectRoot": 0.055, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testGetConfigSearchPaths": 0.047, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigFileValid": 0.712, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigFileNotExists": 0.012, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigFileNotFile": 0.005, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigFileTooLarge": 0.035, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigValid": 0.009, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigNotArray": 0.035, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigMissingRequired": 0.022, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigInvalidUrl": 0.111, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigNumericRange": 0.005, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigInvalidEnum": 0.133, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testMergeWithDefaults": 0.001, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testFileServiceClientWithDefaultConfig": 0.886, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testFileServiceClientWithCustomOptions": 0.614, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testStorageManagerWithDefaultConfig": 0.708, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testStorageManagerWithCustomOptions": 0.715, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testConfigMerging": 0.591, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testEnvironmentVariableOverride": 0.737, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testUserOptionsOverrideEnvironmentVariables": 0.611, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testBackwardCompatibility": 0.798, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testLoadDefaultConfig": 0.72, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testGetConfigValue": 2.667, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testMergeWithUserOptions": 0.795, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testEmptyUserOptions": 1.638, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testGetHttpClientConfig": 0.604, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testGetUploadConfig": 0.626, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testGetDownloadConfig": 0.655, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testConfigCaching": 2.443, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testNestedArrayMerge": 1.09, "Tests\\FileServiceClientDownloadTest::testGetDownloadInfoSuccess": 1.663, "Tests\\FileServiceClientDownloadTest::testGetDownloadInfoMissingData": 0.937, "Tests\\FileServiceClientDownloadTest::testDownloadObjectApLocal": 0.888, "Tests\\FileServiceClientDownloadTest::testDownloadObjectRemote": 0.826, "Tests\\FileServiceClientDownloadTest::testDownloadObjectApLocalFileNotExists": 0.723, "Tests\\FileServiceClientDownloadTest::testDownloadObjectInvalidObjectId": 0.714, "Tests\\FileServiceClientDownloadTest::testDownloadObjectInvalidUserId": 0.776, "Tests\\FileServiceClientDownloadTest::testDownloadObjectPathTraversalAttack": 0.918, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testClientInitialization": 0.94, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testBuildObjectData": 0.694, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testBuildObjectDataWithAllParameters": 0.706, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testSetHeaders": 0.679, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDownloadObjectWithInvalidObjectId": 1.133, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDownloadObjectWithInvalidUserId": 0.836, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDetermineStorageTypeWithExplicitType": 0.447, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDetermineStorageTypeWithStorageObject": 0.49, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDetermineStorageTypeWithStoragePath": 0.393, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDetermineStorageTypeDefault": 0.465, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDownloadFromApLocalWithMissingPath": 0.795, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDownloadFromApLocalWithNonExistentFile": 0.811, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDownloadFromApLocalSuccess": 0.806, "FDMC\\FileServiceClient\\Tests\\StorageManagerTest::testStorageManagerInitialization": 0.542, "FDMC\\FileServiceClient\\Tests\\StorageManagerTest::testBuildStorageData": 0.575, "FDMC\\FileServiceClient\\Tests\\StorageManagerTest::testBuildStorageDataWithAllParameters": 0.492, "FDMC\\FileServiceClient\\Tests\\StorageManagerTest::testSetHeaders": 0.505, "FDMC\\FileServiceClient\\Tests\\StorageManagerTest::testStorageDataTypes": 0.529, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testProcessEnvVarsOverridesConfig": 0.021, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testProcessEnvVarsUsesDefaultWhenEnvNotSet": 0.01, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseBooleanCorrectly": 0.001, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseBooleanThrowsExceptionForInvalidValue": 0.008, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseIntegerCorrectly": 0.011, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseIntegerThrowsExceptionForInvalidValue": 0.001, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseIntegerThrowsExceptionForNegativeValue": 0.001, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseFloatCorrectly": 0.001, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseFloatThrowsExceptionForInvalidValue": 0.001, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseArrayCorrectly": 0.001, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseArrayThrowsExceptionForInvalidJson": 0.016, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseArrayThrowsExceptionForNonArrayJson": 0.005, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseStringCorrectly": 0.001, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testGetExpectedType": 0.001, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testProcessEnvironmentVariable": 0.001, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testProcessEnvironmentVariableHandlesTypeConversionError": 0.001, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testCompleteEnvironmentProcessing": 0.001, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testProcessEnvVarsHandlesTypeConversionErrors": 0.01, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateCompleteValidConfig": 0.013, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateInvalidBaseUrl": 0.002, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateMissingRequiredField": 0, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateOutOfRangeValues": 0.001, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateInvalidEnumValue": 0.002, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateInvalidTypeValues": 0, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateEmptyArray": 0, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testGetValidationRules": 0, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidationResultMethods": 0.009, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testFileNotReadableException": 0.001, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testFileNotFoundException": 0.001, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testFormatInvalidException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testPermissionDeniedException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testValidationFailedException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testTypeConversionFailedException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testRequiredMissingException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testValueOutOfRangeException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testInvalidEnumValueException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testInvalidUrlException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testInvalidArrayException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testSyntaxErrorException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testInvalidReturnTypeException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testMergeConflictException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testCircularDependencyException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testAutoLogging": 0.017, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testLogException": 0.001, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testGetRepairSuggestions": 0.004, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testGetDetailedReport": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testGetErrorDescription": 0, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testBooleanConversionFailedException": 0.001, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testIntegerConversionFailedException": 0, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testFloatConversionFailedException": 0, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testArrayConversionFailedException": 0, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testStringConversionFailedException": 0, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testUnsupportedTypeException": 0, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testGetErrorDescription": 0.001, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testGetRepairSuggestions": 0, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testGetDetailedReport": 0, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testConstructorParameters": 0, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testInheritanceFromConfigurationException": 0.001, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testComplexValueHandling": 0, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testNullValueHandling": 0, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testBooleanValueHandling": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testSingletonInstance": 0.021, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testSetMinLevel": 0.007, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogLevels": 0.002, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogWithContext": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogException": 0.006, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogConfigLoad": 0.005, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogConfigValidation": 0.001, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogTypeConversion": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogTypeConversionFailure": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testAddHandler": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testClearHandlers": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogRecord": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testGetLevelName": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testGetLevelFromName": 0.001, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testMinLevelFiltering": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testMultipleHandlers": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testBasicConfigGet": 2.671, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testGetAllConfig": 0.753, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigSet": 1.355, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testBatchConfigSet": 1.939, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigSetFunction": 3.356, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigForget": 18.402, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigHas": 112.731, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigAll": 17.609, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testInvalidKeyType": 0.003, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testEmptyKey": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testInvalidKeyFormat": 0.005, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testInvalidKeyCharacters": 0.006, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testKeyStartsOrEndsWithDot": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testMissingValueParameter": 0.009, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testEmptyConfigArray": 0.001, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigArrayWithNonStringKey": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testDotNotationAccess": 16.128, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigOverride": 6.602, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testComplexConfigMerge": 11.313, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testDefaultValueTypes": 5.88, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testFunctionArgumentCount": 2.917, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testDefaultValueDetection": 1.352, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testDefaultValueHandling": 11.358, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testRegisterServices": 0.004, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testPublishesConfiguration": 0.002, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testCanRegisterGlobalFunctionWhenConfigNotExists": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testCannotRegisterGlobalFunctionWhenLaravelConfigExists": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testRegisterGlobalConfigFunction": 0.001, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testLoggingMethods": 0.001, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testProvidesServices": 0.001, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testGetDefaultConfigPath": 0.001, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testShouldRegisterGlobalFunction": 0.001, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testLoggingWithoutLogService": 0.008, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testConfigFileExists": 0.003, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testConfigFileStructure": 0.002, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testEnvironmentVariableProcessing": 0.005, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testDefaultValues": 0.002, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testGlobalFunctionsLoaded": 0.001, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testGlobalFunctionBasicUsage": 2.845, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testConfigurationSetting": 1.678, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testConfigurationForget": 1.31, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testConfigurationHas": 2.661, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testGlobalConfigManagerIntegration": 2.129, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishToNative": 0.086, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishWithForceOverwrite": 0.038, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishWithoutOverwriteInNonInteractiveEnvironment": 0.022, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishWithUserConfirmation": 0.032, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testCreateConfigDirectory": 0.02, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testCreateExistingDirectory": 0.013, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testCreateDirectoryWithInvalidPath": 0.009, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testGetPublishStatus": 0.034, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishToInvalidPath": 0.018, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishConfigFile": 0.02, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishToLaravelWithoutLaravelEnvironment": 0.011, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testGetPublishStatusWithAutoDetection": 0.011, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testMergeRecursive": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testMergeConfigs": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testMergeUserOptions": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testApplyEnvironmentOverrides": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testMergeAllConfigs": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testSmartArrayMerge": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testIsIndexedArray": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testDeepCopy": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testValidateMergedConfig": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testHasNestedKey": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testGetConfigDiff": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testEmptyConfigHandling": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testInvalidConfigHandling": 0.001, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testConfigurationPriority": 1.589, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testNestedConfigMerging": 1.778, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testArrayConfigMerging": 1.129, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testEnvironmentVariableTypeConversion": 1.387, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testBatchConfigurationSetting": 1.837, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testConfigurationRemoval": 1.81, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testConfigurationReset": 1.241, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testComplexConfigMergeScenario": 0.611, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testInvalidEnvironmentVariableHandling": 0.705, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testCanRegisterGlobalFunctionLogic": 0.002, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testShouldRegisterGlobalFunctionConditions": 0.005}}