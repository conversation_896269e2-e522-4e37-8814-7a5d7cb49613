{"version": 1, "defects": {"FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testLoadFromNonExistentFileUsesDefaults": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testFindProjectRoot": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testGetConfigSearchPaths": 4, "Tests\\FileServiceClientDownloadTest::testGetDownloadInfoSuccess": 4, "Tests\\FileServiceClientDownloadTest::testGetDownloadInfoMissingData": 4, "Tests\\FileServiceClientDownloadTest::testDownloadObjectApLocal": 4, "Tests\\FileServiceClientDownloadTest::testDownloadObjectRemote": 4, "Tests\\FileServiceClientDownloadTest::testDownloadObjectApLocalFileNotExists": 4, "Tests\\FileServiceClientDownloadTest::testDownloadObjectInvalidObjectId": 4, "Tests\\FileServiceClientDownloadTest::testDownloadObjectInvalidUserId": 4, "Tests\\FileServiceClientDownloadTest::testDownloadObjectPathTraversalAttack": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDetermineStorageTypeWithExplicitType": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDetermineStorageTypeWithStorageObject": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDetermineStorageTypeWithStoragePath": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDetermineStorageTypeDefault": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDownloadFromApLocalWithMissingPath": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDownloadFromApLocalWithNonExistentFile": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDownloadFromApLocalSuccess": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testConfigLoading": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testDotNotationAccess": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testDynamicConfigSetting": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testConfigForget": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testRuntimeConfigPriority": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testGetAllConfig": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testNestedConfigSetting": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testConfigMerging": 4, "FDMC\\FileServiceClient\\Tests\\StorageManagerTest::testStorageManagerInitialization": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testProcessEnvVarsHandlesTypeConversionErrors": 4, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateOutOfRangeValues": 4, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateInvalidEnumValue": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testFileNotReadableException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testFileNotFoundException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testFormatInvalidException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testPermissionDeniedException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testValidationFailedException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testTypeConversionFailedException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testRequiredMissingException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testValueOutOfRangeException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testInvalidEnumValueException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testInvalidUrlException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testInvalidArrayException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testSyntaxErrorException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testInvalidReturnTypeException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testMergeConflictException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testCircularDependencyException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testGetDetailedReport": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testBasicConfigGet": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testDefaultValueTypes": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testFunctionArgumentCount": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigForget": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigHas": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testDotNotationAccess": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testRegisterServices": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testPublishesConfiguration": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testCanRegisterGlobalFunctionWhenConfigNotExists": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testCannotRegisterGlobalFunctionWhenLaravelConfigExists": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testRegisterGlobalConfigFunction": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testLoggingMethods": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testProvidesServices": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testGetDefaultConfigPath": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testShouldRegisterGlobalFunction": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testLoggingWithoutLogService": 4, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testConfigFileExists": 4, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testConfigFileStructure": 4, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testEnvironmentVariableProcessing": 4, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testDefaultValues": 4, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testGlobalFunctionBasicUsage": 4, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testConfigurationSetting": 4, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testConfigurationForget": 4, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testConfigurationHas": 4, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testGlobalConfigManagerIntegration": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testGetAllConfig": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigSet": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testBatchConfigSet": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigSetFunction": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigAll": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigOverride": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testComplexConfigMerge": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testDefaultValueHandling": 4, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishToNative": 4, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishWithForceOverwrite": 4, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishWithoutOverwriteInNonInteractiveEnvironment": 4, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishWithUserConfirmation": 4, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testCreateConfigDirectory": 4, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testGetPublishStatus": 4, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishToInvalidPath": 4, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishConfigFile": 4, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testGetPublishStatusWithAutoDetection": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseIntegerThrowsExceptionForNegativeValue": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseArrayThrowsExceptionForInvalidJson": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseArrayThrowsExceptionForNonArrayJson": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testConfigurationPriority": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testNestedConfigMerging": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testArrayConfigMerging": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testEnvironmentVariableTypeConversion": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testBatchConfigurationSetting": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testConfigurationRemoval": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testConfigurationReset": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testComplexConfigMergeScenario": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testInvalidEnvironmentVariableHandling": 4, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testFileServiceClientWithDefaultConfig": 4, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testFileServiceClientWithCustomOptions": 4, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testStorageManagerWithDefaultConfig": 4, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testStorageManagerWithCustomOptions": 4, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testConfigMerging": 4, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testEnvironmentVariableOverride": 4, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testUserOptionsOverrideEnvironmentVariables": 4, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testBackwardCompatibility": 4, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testLoadDefaultConfig": 4, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testGetConfigValue": 4, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testMergeWithUserOptions": 4, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testEmptyUserOptions": 4, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testGetHttpClientConfig": 4, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testGetUploadConfig": 4, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testGetDownloadConfig": 4, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testConfigCaching": 4, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testNestedArrayMerge": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testCanRegisterGlobalFunctionLogic": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testShouldRegisterGlobalFunctionConditions": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testLoadFromValidConfigFile": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigFileValid": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigFileNotExists": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigFileNotFile": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigFileTooLarge": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigValid": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigNotArray": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigMissingRequired": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigInvalidUrl": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigNumericRange": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigInvalidEnum": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testMergeWithDefaults": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testSingletonInstance": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testSetMinLevel": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogLevels": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogWithContext": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogConfigLoad": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogConfigValidation": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogTypeConversion": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogTypeConversionFailure": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testAddHandler": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testClearHandlers": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogRecord": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testGetLevelName": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testGetLevelFromName": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testMinLevelFiltering": 4, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testMultipleHandlers": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testMergeRecursive": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testMergeConfigs": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testMergeUserOptions": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testApplyEnvironmentOverrides": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testMergeAllConfigs": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testSmartArrayMerge": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testIsIndexedArray": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testDeepCopy": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testValidateMergedConfig": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testHasNestedKey": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testGetConfigDiff": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testEmptyConfigHandling": 4, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testInvalidConfigHandling": 4, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testCreateExistingDirectory": 4, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testCreateDirectoryWithInvalidPath": 4, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishToLaravelWithoutLaravelEnvironment": 4, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateCompleteValidConfig": 4, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateInvalidBaseUrl": 4, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateMissingRequiredField": 4, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateInvalidTypeValues": 4, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateEmptyArray": 4, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testGetValidationRules": 4, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidationResultMethods": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testAutoLogging": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testLogException": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testGetRepairSuggestions": 4, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testGetErrorDescription": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testProcessEnvVarsOverridesConfig": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testProcessEnvVarsUsesDefaultWhenEnvNotSet": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseBooleanCorrectly": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseBooleanThrowsExceptionForInvalidValue": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseIntegerCorrectly": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseIntegerThrowsExceptionForInvalidValue": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseFloatCorrectly": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseFloatThrowsExceptionForInvalidValue": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseArrayCorrectly": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseStringCorrectly": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testGetExpectedType": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testProcessEnvironmentVariable": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testProcessEnvironmentVariableHandlesTypeConversionError": 4, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testCompleteEnvironmentProcessing": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testClientInitialization": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testBuildObjectData": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testBuildObjectDataWithAllParameters": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testSetHeaders": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDownloadObjectWithInvalidObjectId": 4, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDownloadObjectWithInvalidUserId": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testInvalidKeyType": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testEmptyKey": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testInvalidKeyFormat": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testInvalidKeyCharacters": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testKeyStartsOrEndsWithDot": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testMissingValueParameter": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testEmptyConfigArray": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigArrayWithNonStringKey": 4, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testEnvironmentDetection": 4, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testGlobalFunctionsLoaded": 4, "FDMC\\FileServiceClient\\Tests\\StorageManagerTest::testBuildStorageData": 4, "FDMC\\FileServiceClient\\Tests\\StorageManagerTest::testBuildStorageDataWithAllParameters": 4, "FDMC\\FileServiceClient\\Tests\\StorageManagerTest::testSetHeaders": 4, "FDMC\\FileServiceClient\\Tests\\StorageManagerTest::testStorageDataTypes": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testBooleanConversionFailedException": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testIntegerConversionFailedException": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testFloatConversionFailedException": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testArrayConversionFailedException": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testStringConversionFailedException": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testUnsupportedTypeException": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testGetErrorDescription": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testGetRepairSuggestions": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testGetDetailedReport": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testConstructorParameters": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testInheritanceFromConfigurationException": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testComplexValueHandling": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testNullValueHandling": 4, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testBooleanValueHandling": 4}, "times": {"FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testEnvironmentDetection": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testConfigLoading": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testDotNotationAccess": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testDynamicConfigSetting": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testConfigForget": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testRuntimeConfigPriority": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testGetAllConfig": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testNestedConfigSetting": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigManagerTest::testConfigMerging": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testLoadFromValidConfigFile": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testLoadFromNonExistentFileUsesDefaults": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testFindProjectRoot": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testGetConfigSearchPaths": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigFileValid": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigFileNotExists": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigFileNotFile": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigFileTooLarge": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigValid": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigNotArray": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigMissingRequired": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigInvalidUrl": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigNumericRange": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testValidateConfigInvalidEnum": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoaderTest::testMergeWithDefaults": 0, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testFileServiceClientWithDefaultConfig": 0.004, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testFileServiceClientWithCustomOptions": 0, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testStorageManagerWithDefaultConfig": 0, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testStorageManagerWithCustomOptions": 0, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testConfigMerging": 0, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testEnvironmentVariableOverride": 0, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testUserOptionsOverrideEnvironmentVariables": 0, "FDMC\\FileServiceClient\\Tests\\ConfigIntegrationTest::testBackwardCompatibility": 0, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testLoadDefaultConfig": 0, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testGetConfigValue": 0, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testMergeWithUserOptions": 0, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testEmptyUserOptions": 0, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testGetHttpClientConfig": 0, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testGetUploadConfig": 0, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testGetDownloadConfig": 0, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testConfigCaching": 0, "FDMC\\FileServiceClient\\Tests\\ConfigManagerTest::testNestedArrayMerge": 0, "Tests\\FileServiceClientDownloadTest::testGetDownloadInfoSuccess": 0.014, "Tests\\FileServiceClientDownloadTest::testGetDownloadInfoMissingData": 0, "Tests\\FileServiceClientDownloadTest::testDownloadObjectApLocal": 0, "Tests\\FileServiceClientDownloadTest::testDownloadObjectRemote": 0, "Tests\\FileServiceClientDownloadTest::testDownloadObjectApLocalFileNotExists": 0, "Tests\\FileServiceClientDownloadTest::testDownloadObjectInvalidObjectId": 0, "Tests\\FileServiceClientDownloadTest::testDownloadObjectInvalidUserId": 0, "Tests\\FileServiceClientDownloadTest::testDownloadObjectPathTraversalAttack": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testClientInitialization": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testBuildObjectData": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testBuildObjectDataWithAllParameters": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testSetHeaders": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDownloadObjectWithInvalidObjectId": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDownloadObjectWithInvalidUserId": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDetermineStorageTypeWithExplicitType": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDetermineStorageTypeWithStorageObject": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDetermineStorageTypeWithStoragePath": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDetermineStorageTypeDefault": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDownloadFromApLocalWithMissingPath": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDownloadFromApLocalWithNonExistentFile": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceClientTest::testDownloadFromApLocalSuccess": 0, "FDMC\\FileServiceClient\\Tests\\StorageManagerTest::testStorageManagerInitialization": 0, "FDMC\\FileServiceClient\\Tests\\StorageManagerTest::testBuildStorageData": 0, "FDMC\\FileServiceClient\\Tests\\StorageManagerTest::testBuildStorageDataWithAllParameters": 0, "FDMC\\FileServiceClient\\Tests\\StorageManagerTest::testSetHeaders": 0, "FDMC\\FileServiceClient\\Tests\\StorageManagerTest::testStorageDataTypes": 0, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testProcessEnvVarsOverridesConfig": 0, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testProcessEnvVarsUsesDefaultWhenEnvNotSet": 0, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseBooleanCorrectly": 0, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseBooleanThrowsExceptionForInvalidValue": 0, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseIntegerCorrectly": 0, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseIntegerThrowsExceptionForInvalidValue": 0, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseIntegerThrowsExceptionForNegativeValue": 0, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseFloatCorrectly": 0, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseFloatThrowsExceptionForInvalidValue": 0, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseArrayCorrectly": 0, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseArrayThrowsExceptionForInvalidJson": 0, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseArrayThrowsExceptionForNonArrayJson": 0, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testParseStringCorrectly": 0, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testGetExpectedType": 0, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testProcessEnvironmentVariable": 0, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testProcessEnvironmentVariableHandlesTypeConversionError": 0, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testCompleteEnvironmentProcessing": 0, "FDMC\\FileServiceClient\\Tests\\EnvironmentProcessorTest::testProcessEnvVarsHandlesTypeConversionErrors": 0, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateCompleteValidConfig": 0, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateInvalidBaseUrl": 0, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateMissingRequiredField": 0, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateOutOfRangeValues": 0, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateInvalidEnumValue": 0, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateInvalidTypeValues": 0, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidateEmptyArray": 0, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testGetValidationRules": 0, "FDMC\\FileServiceClient\\Tests\\ConfigValidatorTest::testValidationResultMethods": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testFileNotReadableException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testFileNotFoundException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testFormatInvalidException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testPermissionDeniedException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testValidationFailedException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testTypeConversionFailedException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testRequiredMissingException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testValueOutOfRangeException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testInvalidEnumValueException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testInvalidUrlException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testInvalidArrayException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testSyntaxErrorException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testInvalidReturnTypeException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testMergeConflictException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testCircularDependencyException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testAutoLogging": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testLogException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testGetRepairSuggestions": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testGetDetailedReport": 0, "FDMC\\FileServiceClient\\Tests\\ConfigurationExceptionTest::testGetErrorDescription": 0, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testBooleanConversionFailedException": 0, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testIntegerConversionFailedException": 0, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testFloatConversionFailedException": 0, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testArrayConversionFailedException": 0, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testStringConversionFailedException": 0, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testUnsupportedTypeException": 0, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testGetErrorDescription": 0, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testGetRepairSuggestions": 0, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testGetDetailedReport": 0, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testConstructorParameters": 0, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testInheritanceFromConfigurationException": 0, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testComplexValueHandling": 0, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testNullValueHandling": 0, "FDMC\\FileServiceClient\\Tests\\TypeConversionExceptionTest::testBooleanValueHandling": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testSingletonInstance": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testSetMinLevel": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogLevels": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogWithContext": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogException": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogConfigLoad": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogConfigValidation": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogTypeConversion": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogTypeConversionFailure": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testAddHandler": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testClearHandlers": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testLogRecord": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testGetLevelName": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testGetLevelFromName": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testMinLevelFiltering": 0, "FDMC\\FileServiceClient\\Tests\\ConfigLoggerTest::testMultipleHandlers": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testBasicConfigGet": 0.001, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testGetAllConfig": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigSet": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testBatchConfigSet": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigSetFunction": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigForget": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigHas": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigAll": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testInvalidKeyType": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testEmptyKey": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testInvalidKeyFormat": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testInvalidKeyCharacters": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testKeyStartsOrEndsWithDot": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testMissingValueParameter": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testEmptyConfigArray": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigArrayWithNonStringKey": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testDotNotationAccess": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testConfigOverride": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testComplexConfigMerge": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testDefaultValueTypes": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testFunctionArgumentCount": 0, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testDefaultValueDetection": 1.352, "FDMC\\FileServiceClient\\Tests\\GlobalConfigFunctionTest::testDefaultValueHandling": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testRegisterServices": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testPublishesConfiguration": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testCanRegisterGlobalFunctionWhenConfigNotExists": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testCannotRegisterGlobalFunctionWhenLaravelConfigExists": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testRegisterGlobalConfigFunction": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testLoggingMethods": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testProvidesServices": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testGetDefaultConfigPath": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testShouldRegisterGlobalFunction": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testLoggingWithoutLogService": 0, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testConfigFileExists": 0.02, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testConfigFileStructure": 0.002, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testEnvironmentVariableProcessing": 0.002, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testDefaultValues": 0.001, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testGlobalFunctionsLoaded": 0, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testGlobalFunctionBasicUsage": 0.453, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testConfigurationSetting": 0.349, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testConfigurationForget": 0.244, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testConfigurationHas": 0.472, "FDMC\\FileServiceClient\\Tests\\ServiceProviderCoreTest::testGlobalConfigManagerIntegration": 0, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishToNative": 0.002, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishWithForceOverwrite": 0.001, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishWithoutOverwriteInNonInteractiveEnvironment": 0, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishWithUserConfirmation": 0.001, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testCreateConfigDirectory": 0, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testCreateExistingDirectory": 0.001, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testCreateDirectoryWithInvalidPath": 0, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testGetPublishStatus": 0, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishToInvalidPath": 0, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishConfigFile": 0, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testPublishToLaravelWithoutLaravelEnvironment": 0, "FDMC\\FileServiceClient\\Tests\\ConfigPublisherTest::testGetPublishStatusWithAutoDetection": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testMergeRecursive": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testMergeConfigs": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testMergeUserOptions": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testApplyEnvironmentOverrides": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testMergeAllConfigs": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testSmartArrayMerge": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testIsIndexedArray": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testDeepCopy": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testValidateMergedConfig": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testHasNestedKey": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testGetConfigDiff": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testEmptyConfigHandling": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergerTest::testInvalidConfigHandling": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testConfigurationPriority": 0.001, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testNestedConfigMerging": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testArrayConfigMerging": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testEnvironmentVariableTypeConversion": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testBatchConfigurationSetting": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testConfigurationRemoval": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testConfigurationReset": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testComplexConfigMergeScenario": 0, "FDMC\\FileServiceClient\\Tests\\ConfigMergeIntegrationTest::testInvalidEnvironmentVariableHandling": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testCanRegisterGlobalFunctionLogic": 0, "FDMC\\FileServiceClient\\Tests\\FileServiceConfigServiceProviderTest::testShouldRegisterGlobalFunctionConditions": 0}}