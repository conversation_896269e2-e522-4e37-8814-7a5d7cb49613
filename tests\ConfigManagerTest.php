<?php

namespace FDMC\FileServiceClient\Tests;

use PHPUnit\Framework\TestCase;
use FDMC\FileServiceClient\ConfigManager;
use FDMC\FileServiceClient\FileServiceException;

class ConfigManagerTest extends TestCase
{
    protected function setUp(): void
    {
        // 模擬 env 函數
        $this->mockEnvFunction();

        // 清除配置快取
        ConfigManager::clearCache();
    }

    /**
     * 模擬 Laravel 的 env() 函數
     */
    private function mockEnvFunction(): void
    {
        if (!function_exists('env')) {
            eval ('
                function env($key, $default = null) {
                    $value = $_ENV[$key] ?? getenv($key);
                    if ($value === false) {
                        return $default;
                    }
                    return $value;
                }
            ');
        }
    }

    protected function tearDown(): void
    {
        // 清除配置快取
        ConfigManager::clearCache();
    }

    public function testLoadDefaultConfig()
    {
        $config = ConfigManager::all();

        $this->assertIsArray($config);
        $this->assertArrayHasKey('baseUrl', $config);
        $this->assertArrayHasKey('http', $config);
        $this->assertArrayHasKey('headers', $config);
    }

    public function testGetConfigValue()
    {
        // 測試獲取頂層配置
        $baseUrl = ConfigManager::get('baseUrl');
        $this->assertIsString($baseUrl);

        // 測試獲取巢狀配置
        $timeout = ConfigManager::get('http.timeout');
        $this->assertIsInt($timeout);

        // 測試獲取不存在的配置（返回預設值）
        $nonExistent = ConfigManager::get('non.existent.key', 'default');
        $this->assertEquals('default', $nonExistent);
    }

    public function testMergeWithUserOptions()
    {
        $userOptions = [
            'baseUrl' => 'https://custom.api.com',
            'http' => [
                'timeout' => 60,
                // connect_timeout 不設置，應該保持預設值
            ],
            'headers' => [
                'Authorization' => 'Bearer token',
                // Content-Type 不設置，應該保持預設值
            ],
        ];

        $mergedConfig = ConfigManager::mergeWithUserOptions($userOptions);

        // 檢查使用者選項是否正確覆蓋
        $this->assertEquals('https://custom.api.com', $mergedConfig['baseUrl']);
        $this->assertEquals(60, $mergedConfig['http']['timeout']);
        $this->assertEquals('Bearer token', $mergedConfig['headers']['Authorization']);

        // 檢查預設值是否保留
        $this->assertArrayHasKey('connect_timeout', $mergedConfig['http']);
        $this->assertEquals('application/json', $mergedConfig['headers']['Content-Type']);
    }

    public function testEmptyUserOptions()
    {
        $defaultConfig = ConfigManager::all();
        $mergedConfig = ConfigManager::mergeWithUserOptions([]);

        $this->assertEquals($defaultConfig, $mergedConfig);
    }

    public function testGetHttpClientConfig()
    {
        $userOptions = [
            'baseUrl' => 'https://test.api.com',
            'http' => [
                'timeout' => 45,
            ],
        ];

        $httpConfig = ConfigManager::getHttpClientConfig($userOptions);

        $this->assertArrayHasKey('baseUrl', $httpConfig);
        $this->assertArrayHasKey('timeout', $httpConfig);
        $this->assertArrayHasKey('connect_timeout', $httpConfig);
        $this->assertArrayHasKey('headers', $httpConfig);

        $this->assertEquals('https://test.api.com', $httpConfig['baseUrl']);
        $this->assertEquals(45, $httpConfig['timeout']);
    }

    public function testGetUploadConfig()
    {
        $uploadConfig = ConfigManager::getUploadConfig();

        $this->assertIsArray($uploadConfig);
        $this->assertArrayHasKey('timeout', $uploadConfig);
        $this->assertArrayHasKey('max_file_size', $uploadConfig);
        $this->assertArrayHasKey('allowed_mime_types', $uploadConfig);
    }

    public function testGetDownloadConfig()
    {
        $downloadConfig = ConfigManager::getDownloadConfig();

        $this->assertIsArray($downloadConfig);
        $this->assertArrayHasKey('timeout', $downloadConfig);
        $this->assertArrayHasKey('stream_buffer_size', $downloadConfig);
        $this->assertArrayHasKey('large_file_threshold', $downloadConfig);
    }

    public function testConfigCaching()
    {
        // 第一次載入
        $config1 = ConfigManager::all();

        // 第二次載入（應該使用快取）
        $config2 = ConfigManager::all();

        $this->assertEquals($config1, $config2);

        // 清除快取後重新載入
        ConfigManager::clearCache();
        $config3 = ConfigManager::all();

        $this->assertEquals($config1, $config3);
    }

    public function testNestedArrayMerge()
    {
        $userOptions = [
            'http' => [
                'timeout' => 100,
                'new_option' => 'new_value',
            ],
            'headers' => [
                'Authorization' => 'Bearer new-token',
                'X-Custom' => 'custom-value',
            ],
            'new_section' => [
                'option1' => 'value1',
            ],
        ];

        $mergedConfig = ConfigManager::mergeWithUserOptions($userOptions);

        // 檢查深層合併
        $this->assertEquals(100, $mergedConfig['http']['timeout']);
        $this->assertEquals('new_value', $mergedConfig['http']['new_option']);
        $this->assertArrayHasKey('connect_timeout', $mergedConfig['http']); // 預設值保留

        $this->assertEquals('Bearer new-token', $mergedConfig['headers']['Authorization']);
        $this->assertEquals('custom-value', $mergedConfig['headers']['X-Custom']);
        $this->assertEquals('application/json', $mergedConfig['headers']['Content-Type']); // 預設值保留

        $this->assertEquals('value1', $mergedConfig['new_section']['option1']);
    }
}