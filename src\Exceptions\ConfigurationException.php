<?php

declare(strict_types=1);

namespace FDMC\FileServiceClient;

/**
 * 配置例外類別
 * 
 * 處理配置相關的錯誤，提供詳細的錯誤訊息和修復建議。
 */
class ConfigurationException extends FileServiceException
{
    // 錯誤碼定義
    public const CONFIG_FILE_NOT_READABLE = 1001;
    public const CONFIG_FORMAT_INVALID = 1002;
    public const CONFIG_PERMISSION_DENIED = 1003;
    public const CONFIG_VALIDATION_FAILED = 1004;
    public const CONFIG_TYPE_CONVERSION_FAILED = 1005;
    public const CONFIG_REQUIRED_MISSING = 1006;
    public const CONFIG_VALUE_OUT_OF_RANGE = 1007;
    public const CONFIG_INVALID_ENUM_VALUE = 1008;
    public const CONFIG_INVALID_URL = 1009;
    public const CONFIG_INVALID_ARRAY = 1010;
    public const CONFIG_INVALID_PARAMETER = 1011;
    public const CONFIG_MISSING_PARAMETER = 1012;
    public const CONFIG_INVALID_KEY = 1013;
    public const CONFIG_PUBLISH_FAILED = 1014;

    /**
     * 驗證結果（如果適用）
     */
    private ?ValidationResult $validationResult = null;

    /**
     * 是否已記錄日誌
     */
    private bool $logged = false;

    /**
     * 設定驗證結果
     *
     * @param ValidationResult $result 驗證結果
     * @return self
     */
    public function setValidationResult(ValidationResult $result): self
    {
        $this->validationResult = $result;
        return $this;
    }

    /**
     * 獲取驗證結果
     *
     * @return ValidationResult|null 驗證結果
     */
    public function getValidationResult(): ?ValidationResult
    {
        return $this->validationResult;
    }

    /**
     * 配置檔案無法讀取
     *
     * @param string $path 檔案路徑
     * @param bool $autoLog 是否自動記錄日誌
     * @return self
     */
    public static function fileNotReadable(string $path, bool $autoLog = true): self
    {
        $exception = new self(
            "配置檔案無法讀取：{$path}",
            self::CONFIG_FILE_NOT_READABLE
        );
        
        if ($autoLog) {
            $exception->logException();
        }
        
        return $exception;
    }

    /**
     * 配置檔案格式錯誤
     *
     * @param string $path 檔案路徑
     * @param string $reason 錯誤原因
     * @param bool $autoLog 是否自動記錄日誌
     * @return self
     */
    public static function formatInvalid(string $path, string $reason, bool $autoLog = true): self
    {
        $exception = new self(
            "配置檔案格式錯誤 {$path}：{$reason}",
            self::CONFIG_FORMAT_INVALID
        );
        
        if ($autoLog) {
            $exception->logException();
        }
        
        return $exception;
    }

    /**
     * 配置檔案權限不足
     *
     * @param string $path 檔案路徑
     * @param bool $autoLog 是否自動記錄日誌
     * @return self
     */
    public static function permissionDenied(string $path, bool $autoLog = true): self
    {
        $exception = new self(
            "配置檔案權限不足：{$path}",
            self::CONFIG_PERMISSION_DENIED
        );
        
        if ($autoLog) {
            $exception->logException();
        }
        
        return $exception;
    }

    /**
     * 配置驗證失敗
     *
     * @param ValidationResult $result 驗證結果
     * @param bool $autoLog 是否自動記錄日誌
     * @return self
     */
    public static function validationFailed(ValidationResult $result, bool $autoLog = true): self
    {
        $exception = new self(
            "配置驗證失敗：" . $result->getSummary(),
            self::CONFIG_VALIDATION_FAILED
        );
        
        $exception->setValidationResult($result);
        
        if ($autoLog) {
            $exception->logException();
        }
        
        return $exception;
    }

    /**
     * 環境變數型別轉換失敗
     *
     * @param string $key 環境變數鍵
     * @param string $value 環境變數值
     * @param string $expectedType 期望型別
     * @param bool $autoLog 是否自動記錄日誌
     * @return self
     */
    public static function typeConversionFailed(string $key, string $value, string $expectedType, bool $autoLog = true): self
    {
        $exception = new self(
            "環境變數 {$key} 型別轉換失敗：無法將 '{$value}' 轉換為 {$expectedType}",
            self::CONFIG_TYPE_CONVERSION_FAILED
        );
        
        if ($autoLog) {
            $exception->logException(ConfigLogger::LEVEL_WARNING);
        }
        
        return $exception;
    }

    /**
     * 必要配置項目缺失
     *
     * @param string $key 配置鍵
     * @param bool $autoLog 是否自動記錄日誌
     * @return self
     */
    public static function requiredMissing(string $key, bool $autoLog = true): self
    {
        $exception = new self(
            "必要配置項目缺失：{$key}",
            self::CONFIG_REQUIRED_MISSING
        );
        
        if ($autoLog) {
            $exception->logException();
        }
        
        return $exception;
    }

    /**
     * 配置值超出範圍
     *
     * @param string $key 配置鍵
     * @param mixed $value 配置值
     * @param mixed $min 最小值
     * @param mixed $max 最大值
     * @param bool $autoLog 是否自動記錄日誌
     * @return self
     */
    public static function valueOutOfRange(string $key, mixed $value, mixed $min = null, mixed $max = null, bool $autoLog = true): self
    {
        $range = '';
        if ($min !== null && $max !== null) {
            $range = " (範圍：{$min} - {$max})";
        } elseif ($min !== null) {
            $range = " (最小值：{$min})";
        } elseif ($max !== null) {
            $range = " (最大值：{$max})";
        }
        
        $exception = new self(
            "配置項目 {$key} 值 {$value} 超出允許範圍{$range}",
            self::CONFIG_VALUE_OUT_OF_RANGE
        );
        
        if ($autoLog) {
            $exception->logException(ConfigLogger::LEVEL_WARNING);
        }
        
        return $exception;
    }

    /**
     * 無效的枚舉值
     *
     * @param string $key 配置鍵
     * @param mixed $value 配置值
     * @param array $allowedValues 允許的值
     * @param bool $autoLog 是否自動記錄日誌
     * @return self
     */
    public static function invalidEnumValue(string $key, mixed $value, array $allowedValues, bool $autoLog = true): self
    {
        $allowed = implode(', ', $allowedValues);
        $exception = new self(
            "配置項目 {$key} 值 '{$value}' 不在允許的值列表中：{$allowed}",
            self::CONFIG_INVALID_ENUM_VALUE
        );
        
        if ($autoLog) {
            $exception->logException(ConfigLogger::LEVEL_WARNING);
        }
        
        return $exception;
    }

    /**
     * 無效的 URL 格式
     *
     * @param string $key 配置鍵
     * @param string $value 配置值
     * @param bool $autoLog 是否自動記錄日誌
     * @return self
     */
    public static function invalidUrl(string $key, string $value, bool $autoLog = true): self
    {
        $exception = new self(
            "配置項目 {$key} 不是有效的 URL 格式：{$value}",
            self::CONFIG_INVALID_URL
        );
        
        if ($autoLog) {
            $exception->logException(ConfigLogger::LEVEL_WARNING);
        }
        
        return $exception;
    }

    /**
     * 無效的陣列格式
     *
     * @param string $key 配置鍵
     * @param string $reason 錯誤原因
     * @param bool $autoLog 是否自動記錄日誌
     * @return self
     */
    public static function invalidArray(string $key, string $reason, bool $autoLog = true): self
    {
        $exception = new self(
            "配置項目 {$key} 陣列格式錯誤：{$reason}",
            self::CONFIG_INVALID_ARRAY
        );
        
        if ($autoLog) {
            $exception->logException(ConfigLogger::LEVEL_WARNING);
        }
        
        return $exception;
    }

    /**
     * 配置發布失敗
     *
     * @param string $reason 失敗原因
     * @param bool $autoLog 是否自動記錄日誌
     * @return self
     */
    public static function publishFailed(string $reason, bool $autoLog = true): self
    {
        $exception = new self(
            "配置發布失敗：{$reason}",
            self::CONFIG_PUBLISH_FAILED
        );
        
        if ($autoLog) {
            $exception->logException();
        }
        
        return $exception;
    }

    /**
     * 獲取錯誤碼對應的描述
     *
     * @return string 錯誤描述
     */
    public function getErrorDescription(): string
    {
        return match ($this->getCode()) {
            self::CONFIG_FILE_NOT_READABLE => '配置檔案無法讀取',
            self::CONFIG_FORMAT_INVALID => '配置檔案格式錯誤',
            self::CONFIG_PERMISSION_DENIED => '配置檔案權限不足',
            self::CONFIG_VALIDATION_FAILED => '配置驗證失敗',
            self::CONFIG_TYPE_CONVERSION_FAILED => '型別轉換失敗',
            self::CONFIG_REQUIRED_MISSING => '必要配置項目缺失',
            self::CONFIG_VALUE_OUT_OF_RANGE => '配置值超出範圍',
            self::CONFIG_INVALID_ENUM_VALUE => '無效的枚舉值',
            self::CONFIG_INVALID_URL => '無效的 URL 格式',
            self::CONFIG_INVALID_ARRAY => '無效的陣列格式',
            self::CONFIG_INVALID_PARAMETER => '無效的參數',
            self::CONFIG_MISSING_PARAMETER => '缺少必要參數',
            self::CONFIG_INVALID_KEY => '無效的配置鍵',
            self::CONFIG_PUBLISH_FAILED => '配置發布失敗',
            default => '未知的配置錯誤'
        };
    }

    /**
     * 獲取修復建議
     *
     * @return array 修復建議列表
     */
    public function getRepairSuggestions(): array
    {
        if ($this->validationResult) {
            return $this->validationResult->getRepairSuggestions();
        }

        return match ($this->getCode()) {
            self::CONFIG_FILE_NOT_READABLE => [
                '檢查檔案是否存在',
                '檢查檔案權限是否正確',
                '確認檔案路徑是否正確'
            ],
            self::CONFIG_FORMAT_INVALID => [
                '檢查 PHP 語法是否正確',
                '確認配置檔案返回陣列',
                '檢查是否有語法錯誤'
            ],
            self::CONFIG_PERMISSION_DENIED => [
                '設定適當的檔案權限（如 644）',
                '確認 Web 伺服器有讀取權限'
            ],
            self::CONFIG_TYPE_CONVERSION_FAILED => [
                '檢查環境變數值的格式',
                '確認型別轉換規則是否正確'
            ],
            self::CONFIG_PUBLISH_FAILED => [
                '檢查目標目錄是否存在且可寫',
                '確認來源配置檔案是否存在',
                '檢查檔案權限設定'
            ],
            default => ['請檢查配置檔案的格式和內容']
        };
    }

    /**
     * 記錄例外到日誌
     *
     * @param int $level 日誌級別
     * @return self
     */
    public function logException(int $level = ConfigLogger::LEVEL_ERROR): self
    {
        if (!$this->logged) {
            $logger = ConfigLogger::getInstance();
            $logger->logException($this, $level, [
                'error_code' => $this->getCode(),
                'error_description' => $this->getErrorDescription(),
            ]);
            $this->logged = true;
        }
        return $this;
    }

    /**
     * 檢查是否已記錄日誌
     *
     * @return bool
     */
    public function isLogged(): bool
    {
        return $this->logged;
    }

    /**
     * 配置檔案不存在
     *
     * @param string $path 檔案路徑
     * @param bool $autoLog 是否自動記錄日誌
     * @return self
     */
    public static function fileNotFound(string $path, bool $autoLog = true): self
    {
        $exception = new self(
            "配置檔案不存在：{$path}",
            self::CONFIG_FILE_NOT_READABLE
        );
        
        if ($autoLog) {
            $exception->logException(ConfigLogger::LEVEL_WARNING);
        }
        
        return $exception;
    }

    /**
     * 配置目錄不存在
     *
     * @param string $path 目錄路徑
     * @param bool $autoLog 是否自動記錄日誌
     * @return self
     */
    public static function directoryNotFound(string $path, bool $autoLog = true): self
    {
        $exception = new self(
            "配置目錄不存在：{$path}",
            self::CONFIG_FILE_NOT_READABLE
        );
        
        if ($autoLog) {
            $exception->logException(ConfigLogger::LEVEL_WARNING);
        }
        
        return $exception;
    }

    /**
     * 配置檔案為空
     *
     * @param string $path 檔案路徑
     * @param bool $autoLog 是否自動記錄日誌
     * @return self
     */
    public static function fileEmpty(string $path, bool $autoLog = true): self
    {
        $exception = new self(
            "配置檔案為空：{$path}",
            self::CONFIG_FORMAT_INVALID
        );
        
        if ($autoLog) {
            $exception->logException(ConfigLogger::LEVEL_WARNING);
        }
        
        return $exception;
    }

    /**
     * 配置檔案語法錯誤
     *
     * @param string $path 檔案路徑
     * @param string $syntaxError 語法錯誤詳情
     * @param bool $autoLog 是否自動記錄日誌
     * @return self
     */
    public static function syntaxError(string $path, string $syntaxError, bool $autoLog = true): self
    {
        $exception = new self(
            "配置檔案語法錯誤 {$path}：{$syntaxError}",
            self::CONFIG_FORMAT_INVALID
        );
        
        if ($autoLog) {
            $exception->logException();
        }
        
        return $exception;
    }

    /**
     * 配置檔案不返回陣列
     *
     * @param string $path 檔案路徑
     * @param string $actualType 實際返回的型別
     * @param bool $autoLog 是否自動記錄日誌
     * @return self
     */
    public static function invalidReturnType(string $path, string $actualType, bool $autoLog = true): self
    {
        $exception = new self(
            "配置檔案 {$path} 必須返回陣列，實際返回：{$actualType}",
            self::CONFIG_FORMAT_INVALID
        );
        
        if ($autoLog) {
            $exception->logException();
        }
        
        return $exception;
    }

    /**
     * 環境變數格式錯誤
     *
     * @param string $key 環境變數鍵
     * @param string $value 環境變數值
     * @param string $expectedFormat 期望格式
     * @param bool $autoLog 是否自動記錄日誌
     * @return self
     */
    public static function invalidEnvironmentFormat(string $key, string $value, string $expectedFormat, bool $autoLog = true): self
    {
        $exception = new self(
            "環境變數 {$key} 格式錯誤：期望 {$expectedFormat}，實際值：{$value}",
            self::CONFIG_TYPE_CONVERSION_FAILED
        );
        
        if ($autoLog) {
            $exception->logException(ConfigLogger::LEVEL_WARNING);
        }
        
        return $exception;
    }

    /**
     * 配置合併衝突
     *
     * @param string $key 配置鍵
     * @param mixed $value1 第一個值
     * @param mixed $value2 第二個值
     * @param bool $autoLog 是否自動記錄日誌
     * @return self
     */
    public static function mergeConflict(string $key, mixed $value1, mixed $value2, bool $autoLog = true): self
    {
        $exception = new self(
            "配置合併衝突 {$key}：無法合併 " . gettype($value1) . " 和 " . gettype($value2),
            self::CONFIG_VALIDATION_FAILED
        );
        
        if ($autoLog) {
            $exception->logException(ConfigLogger::LEVEL_WARNING);
        }
        
        return $exception;
    }

    /**
     * 循環依賴錯誤
     *
     * @param array $dependencyChain 依賴鏈
     * @param bool $autoLog 是否自動記錄日誌
     * @return self
     */
    public static function circularDependency(array $dependencyChain, bool $autoLog = true): self
    {
        $chain = implode(' -> ', $dependencyChain);
        $exception = new self(
            "配置循環依賴：{$chain}",
            self::CONFIG_VALIDATION_FAILED
        );
        
        if ($autoLog) {
            $exception->logException();
        }
        
        return $exception;
    }

    /**
     * 無效的參數
     *
     * @param string $parameterName 參數名稱
     * @param string $expectedType 期望型別
     * @param string $actualType 實際型別
     * @param bool $autoLog 是否自動記錄日誌
     * @return self
     */
    public static function invalidParameter(string $parameterName, string $expectedType, string $actualType, bool $autoLog = true): self
    {
        $exception = new self(
            "參數 {$parameterName} 型別錯誤：期望 {$expectedType}，實際 {$actualType}",
            self::CONFIG_INVALID_PARAMETER
        );
        
        if ($autoLog) {
            $exception->logException(ConfigLogger::LEVEL_WARNING);
        }
        
        return $exception;
    }

    /**
     * 缺少必要參數
     *
     * @param string $parameterName 參數名稱
     * @param bool $autoLog 是否自動記錄日誌
     * @return self
     */
    public static function missingParameter(string $parameterName, bool $autoLog = true): self
    {
        $exception = new self(
            "缺少必要參數：{$parameterName}",
            self::CONFIG_MISSING_PARAMETER
        );
        
        if ($autoLog) {
            $exception->logException(ConfigLogger::LEVEL_WARNING);
        }
        
        return $exception;
    }

    /**
     * 無效的配置鍵
     *
     * @param string $key 配置鍵
     * @param bool $autoLog 是否自動記錄日誌
     * @return self
     */
    public static function invalidConfigKey(string $key, bool $autoLog = true): self
    {
        $exception = new self(
            "無效的配置鍵格式：{$key}",
            self::CONFIG_INVALID_KEY
        );
        
        if ($autoLog) {
            $exception->logException(ConfigLogger::LEVEL_WARNING);
        }
        
        return $exception;
    }

    /**
     * 獲取詳細的錯誤報告
     *
     * @return string 錯誤報告
     */
    public function getDetailedReport(): string
    {
        $report = [];
        $report[] = "=== 配置錯誤報告 ===";
        $report[] = "錯誤類型：" . $this->getErrorDescription();
        $report[] = "錯誤訊息：" . $this->getMessage();
        $report[] = "錯誤碼：" . $this->getCode();
        $report[] = "發生時間：" . date('Y-m-d H:i:s');
        $report[] = "";
        
        if ($this->validationResult) {
            $report[] = $this->validationResult->getDetailedReport();
        } else {
            $suggestions = $this->getRepairSuggestions();
            if (!empty($suggestions)) {
                $report[] = "修復建議：";
                foreach ($suggestions as $suggestion) {
                    $report[] = "  • " . $suggestion;
                }
            }
        }
        
        return implode("\n", $report);
    }
}