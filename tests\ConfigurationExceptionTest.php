<?php

declare(strict_types=1);

namespace FDMC\FileServiceClient\Tests;

use FDMC\FileServiceClient\ConfigurationException;
use FDMC\FileServiceClient\TypeConversionException;
use FDMC\FileServiceClient\ConfigLogger;
use FDMC\FileServiceClient\ValidationResult;
use PHPUnit\Framework\TestCase;

/**
 * 配置例外測試
 */
class ConfigurationExceptionTest extends TestCase
{
    protected function setUp(): void
    {
        // 清除日誌處理器以避免測試間干擾
        ConfigLogger::getInstance()->clearHandlers();
    }

    public function testFileNotReadableException(): void
    {
        $path = '/nonexistent/config.php';
        $exception = ConfigurationException::fileNotReadable($path, false);
        
        $this->assertInstanceOf(ConfigurationException::class, $exception);
        $this->assertEquals(ConfigurationException::CONFIG_FILE_NOT_READABLE, $exception->getCode());
        $this->assertStringContainsString($path, $exception->getMessage());
        $this->assertEquals('配置檔案無法讀取', $exception->getErrorDescription());
        $this->assertFalse($exception->isLogged());
    }

    public function testFileNotFoundException(): void
    {
        $path = '/missing/config.php';
        $exception = ConfigurationException::fileNotFound($path, false);
        
        $this->assertInstanceOf(ConfigurationException::class, $exception);
        $this->assertEquals(ConfigurationException::CONFIG_FILE_NOT_READABLE, $exception->getCode());
        $this->assertStringContainsString('不存在', $exception->getMessage());
        $this->assertFalse($exception->isLogged());
    }

    public function testFormatInvalidException(): void
    {
        $path = '/invalid/config.php';
        $reason = 'PHP 語法錯誤';
        $exception = ConfigurationException::formatInvalid($path, $reason, false);
        
        $this->assertInstanceOf(ConfigurationException::class, $exception);
        $this->assertEquals(ConfigurationException::CONFIG_FORMAT_INVALID, $exception->getCode());
        $this->assertStringContainsString($path, $exception->getMessage());
        $this->assertStringContainsString($reason, $exception->getMessage());
    }

    public function testPermissionDeniedException(): void
    {
        $path = '/restricted/config.php';
        $exception = ConfigurationException::permissionDenied($path, false);
        
        $this->assertInstanceOf(ConfigurationException::class, $exception);
        $this->assertEquals(ConfigurationException::CONFIG_PERMISSION_DENIED, $exception->getCode());
        $this->assertStringContainsString('權限不足', $exception->getMessage());
    }

    public function testValidationFailedException(): void
    {
        $errors = [
            ['key' => 'baseUrl', 'message' => '無效的 URL 格式', 'type' => 'format', 'description' => '', 'suggestion' => '']
        ];
        $validationResult = new ValidationResult(false, $errors);
        
        $exception = ConfigurationException::validationFailed($validationResult, false);
        
        $this->assertInstanceOf(ConfigurationException::class, $exception);
        $this->assertEquals(ConfigurationException::CONFIG_VALIDATION_FAILED, $exception->getCode());
        $this->assertSame($validationResult, $exception->getValidationResult());
    }

    public function testTypeConversionFailedException(): void
    {
        $key = 'timeout';
        $value = 'invalid';
        $type = 'integer';
        $exception = ConfigurationException::typeConversionFailed($key, $value, $type, false);
        
        $this->assertInstanceOf(ConfigurationException::class, $exception);
        $this->assertEquals(ConfigurationException::CONFIG_TYPE_CONVERSION_FAILED, $exception->getCode());
        $this->assertStringContainsString($key, $exception->getMessage());
        $this->assertStringContainsString($value, $exception->getMessage());
        $this->assertStringContainsString($type, $exception->getMessage());
    }

    public function testRequiredMissingException(): void
    {
        $key = 'baseUrl';
        $exception = ConfigurationException::requiredMissing($key, false);
        
        $this->assertInstanceOf(ConfigurationException::class, $exception);
        $this->assertEquals(ConfigurationException::CONFIG_REQUIRED_MISSING, $exception->getCode());
        $this->assertStringContainsString('缺失', $exception->getMessage());
    }

    public function testValueOutOfRangeException(): void
    {
        $key = 'timeout';
        $value = 1000;
        $min = 1;
        $max = 300;
        $exception = ConfigurationException::valueOutOfRange($key, $value, $min, $max, false);
        
        $this->assertInstanceOf(ConfigurationException::class, $exception);
        $this->assertEquals(ConfigurationException::CONFIG_VALUE_OUT_OF_RANGE, $exception->getCode());
        $this->assertStringContainsString('超出允許範圍', $exception->getMessage());
        $this->assertStringContainsString((string)$min, $exception->getMessage());
        $this->assertStringContainsString((string)$max, $exception->getMessage());
    }

    public function testInvalidEnumValueException(): void
    {
        $key = 'log_level';
        $value = 'invalid';
        $allowedValues = ['debug', 'info', 'warning', 'error'];
        $exception = ConfigurationException::invalidEnumValue($key, $value, $allowedValues, false);
        
        $this->assertInstanceOf(ConfigurationException::class, $exception);
        $this->assertEquals(ConfigurationException::CONFIG_INVALID_ENUM_VALUE, $exception->getCode());
        $this->assertStringContainsString('不在允許的值列表中', $exception->getMessage());
    }

    public function testInvalidUrlException(): void
    {
        $key = 'baseUrl';
        $value = 'not-a-url';
        $exception = ConfigurationException::invalidUrl($key, $value, false);
        
        $this->assertInstanceOf(ConfigurationException::class, $exception);
        $this->assertEquals(ConfigurationException::CONFIG_INVALID_URL, $exception->getCode());
        $this->assertStringContainsString('不是有效的 URL 格式', $exception->getMessage());
    }

    public function testInvalidArrayException(): void
    {
        $key = 'headers';
        $reason = 'JSON 格式錯誤';
        $exception = ConfigurationException::invalidArray($key, $reason, false);
        
        $this->assertInstanceOf(ConfigurationException::class, $exception);
        $this->assertEquals(ConfigurationException::CONFIG_INVALID_ARRAY, $exception->getCode());
        $this->assertStringContainsString('陣列格式錯誤', $exception->getMessage());
    }

    public function testSyntaxErrorException(): void
    {
        $path = '/syntax/error.php';
        $syntaxError = 'Unexpected token';
        $exception = ConfigurationException::syntaxError($path, $syntaxError, false);
        
        $this->assertInstanceOf(ConfigurationException::class, $exception);
        $this->assertEquals(ConfigurationException::CONFIG_FORMAT_INVALID, $exception->getCode());
        $this->assertStringContainsString('語法錯誤', $exception->getMessage());
    }

    public function testInvalidReturnTypeException(): void
    {
        $path = '/invalid/return.php';
        $actualType = 'string';
        $exception = ConfigurationException::invalidReturnType($path, $actualType, false);
        
        $this->assertInstanceOf(ConfigurationException::class, $exception);
        $this->assertEquals(ConfigurationException::CONFIG_FORMAT_INVALID, $exception->getCode());
        $this->assertStringContainsString('必須返回陣列', $exception->getMessage());
    }

    public function testMergeConflictException(): void
    {
        $key = 'config.key';
        $value1 = 'string';
        $value2 = ['array'];
        $exception = ConfigurationException::mergeConflict($key, $value1, $value2, false);
        
        $this->assertInstanceOf(ConfigurationException::class, $exception);
        $this->assertEquals(ConfigurationException::CONFIG_VALIDATION_FAILED, $exception->getCode());
        $this->assertStringContainsString('合併衝突', $exception->getMessage());
    }

    public function testCircularDependencyException(): void
    {
        $dependencyChain = ['config.a', 'config.b', 'config.c', 'config.a'];
        $exception = ConfigurationException::circularDependency($dependencyChain, false);
        
        $this->assertInstanceOf(ConfigurationException::class, $exception);
        $this->assertEquals(ConfigurationException::CONFIG_VALIDATION_FAILED, $exception->getCode());
        $this->assertStringContainsString('循環依賴', $exception->getMessage());
    }

    public function testAutoLogging(): void
    {
        $logged = false;
        
        // 添加測試日誌處理器
        ConfigLogger::getInstance()->addHandler(function (array $record) use (&$logged): void {
            $logged = true;
            $this->assertEquals('file-service-config', $record['channel']);
            $this->assertGreaterThanOrEqual(ConfigLogger::LEVEL_ERROR, $record['level']);
        });
        
        $exception = ConfigurationException::fileNotReadable('/test/path.php', true);
        
        $this->assertTrue($logged);
        $this->assertTrue($exception->isLogged());
    }

    public function testLogException(): void
    {
        $logged = false;
        $loggedLevel = null;
        
        ConfigLogger::getInstance()->addHandler(function (array $record) use (&$logged, &$loggedLevel): void {
            $logged = true;
            $loggedLevel = $record['level'];
        });
        
        $exception = ConfigurationException::fileNotReadable('/test/path.php', false);
        $this->assertFalse($exception->isLogged());
        
        $exception->logException(ConfigLogger::LEVEL_WARNING);
        
        $this->assertTrue($logged);
        $this->assertTrue($exception->isLogged());
        $this->assertEquals(ConfigLogger::LEVEL_WARNING, $loggedLevel);
        
        // 測試重複記錄不會再次觸發
        $logged = false;
        $exception->logException();
        $this->assertFalse($logged);
    }

    public function testGetRepairSuggestions(): void
    {
        $exception = ConfigurationException::fileNotReadable('/test/path.php', false);
        $suggestions = $exception->getRepairSuggestions();
        
        $this->assertIsArray($suggestions);
        $this->assertNotEmpty($suggestions);
        $this->assertContains('檢查檔案是否存在', $suggestions);
    }

    public function testGetDetailedReport(): void
    {
        $exception = ConfigurationException::typeConversionFailed('timeout', 'invalid', 'integer', false);
        $report = $exception->getDetailedReport();
        
        $this->assertIsString($report);
        $this->assertStringContainsString('配置錯誤報告', $report);
        $this->assertStringContainsString('錯誤類型', $report);
        $this->assertStringContainsString('錯誤訊息', $report);
        $this->assertStringContainsString('錯誤碼', $report);
        $this->assertStringContainsString('修復建議', $report);
    }

    public function testGetErrorDescription(): void
    {
        $testCases = [
            [ConfigurationException::CONFIG_FILE_NOT_READABLE, '配置檔案無法讀取'],
            [ConfigurationException::CONFIG_FORMAT_INVALID, '配置檔案格式錯誤'],
            [ConfigurationException::CONFIG_PERMISSION_DENIED, '配置檔案權限不足'],
            [ConfigurationException::CONFIG_VALIDATION_FAILED, '配置驗證失敗'],
            [ConfigurationException::CONFIG_TYPE_CONVERSION_FAILED, '型別轉換失敗'],
            [ConfigurationException::CONFIG_REQUIRED_MISSING, '必要配置項目缺失'],
            [ConfigurationException::CONFIG_VALUE_OUT_OF_RANGE, '配置值超出範圍'],
            [ConfigurationException::CONFIG_INVALID_ENUM_VALUE, '無效的枚舉值'],
            [ConfigurationException::CONFIG_INVALID_URL, '無效的 URL 格式'],
            [ConfigurationException::CONFIG_INVALID_ARRAY, '無效的陣列格式'],
        ];
        
        foreach ($testCases as [$code, $expectedDescription]) {
            $exception = new ConfigurationException('Test message', $code);
            $this->assertEquals($expectedDescription, $exception->getErrorDescription());
        }
        
        // 測試未知錯誤碼
        $exception = new ConfigurationException('Test message', 9999);
        $this->assertEquals('未知的配置錯誤', $exception->getErrorDescription());
    }
}