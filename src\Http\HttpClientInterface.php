<?php

namespace FDMC\FileServiceClient\Http;

use Psr\Http\Message\ResponseInterface;
use FDMC\FileServiceClient\FileServiceException;

/**
 * HTTP 客戶端介面
 * 定義 HTTP 通訊的抽象層，便於測試和擴展
 */
interface HttpClientInterface
{
    /**
     * 發送 JSON API 請求
     * @param string $method 請求方法
     * @param string $endpoint 端點路徑
     * @param array $options 選項配置
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function sendJsonRequest(string $method, string $endpoint, array $options = []): array;

    /**
     * 發送 Multipart 請求
     * @param string $endpoint 端點路徑
     * @param array $options 選項配置
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function sendMultipartRequest(string $endpoint, array $options = []): array;

    /**
     * 發送原始 HTTP 請求
     * @param string $method 請求方法
     * @param string $endpoint 端點路徑
     * @param array $options 選項配置
     * @return ResponseInterface 原始回應
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function sendRawRequest(string $method, string $endpoint, array $options = []): ResponseInterface;

    /**
     * 設置自定義 HTTP 標頭
     * @param array $headers 自定義 HTTP 標頭
     */
    public function setHeaders(array $headers): void;

    /**
     * 獲取當前設置的基礎 URL
     * @return string 基礎 URL
     */
    public function getBaseUrl(): string;
}