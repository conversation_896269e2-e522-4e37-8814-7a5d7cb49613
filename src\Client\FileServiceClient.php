<?php

namespace FDMC\FileServiceClient;

use FDMC\FileServiceClient\Http\HttpClientInterface;
use FDMC\FileServiceClient\Http\GuzzleHttpClient;
use Psr\Http\Message\StreamInterface;

/**
 * File Service Client
 * 用於與 file-service API 溝通的 PHP 客戶端套件
 */
class FileServiceClient
{
    private HttpClientInterface $httpClient;

    /**
     * 初始化客戶端
     * @param array $options 使用者選項，會與配置檔案合併
     * @param HttpClientInterface|null $httpClient 自定義 HTTP 客戶端（用於測試或自定義實現）
     */
    public function __construct(array $options = [], ?HttpClientInterface $httpClient = null)
    {
        // 使用配置管理器合併使用者選項與預設配置
        $mergedConfig = ConfigManager::getHttpClientConfig($options);
        
        $this->httpClient = $httpClient ?? new GuzzleHttpClient($mergedConfig);
    }



    /**
     * 設置自定義 HTTP 標頭
     * @param array $headers 自定義 HTTP 標頭
     */
    public function setHeaders(array $headers): void
    {
        $this->httpClient->setHeaders($headers);
    }

    /**
     * 獲取當前設置的基礎 URL
     * @return string 基礎 URL
     */
    public function getBaseUrl(): string
    {
        return $this->httpClient->getBaseUrl();
    }

    /**
     * 獲取 HTTP 客戶端實例（僅用於進階使用案例）
     * @return HttpClientInterface HTTP 客戶端
     */
    public function getHttpClient(): HttpClientInterface
    {
        return $this->httpClient;
    }

    /**
     * 列出根目錄下的所有物件
     * @param int $companyId 公司 ID
     * @param string $scope 範圍
     * @param int $userId 使用者 ID
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function listRootObjects(int $companyId, string $scope, int $userId): array
    {
        // 防呆機制 1: 驗證公司 ID
        if ($companyId <= 0) {
            throw new FileServiceException("公司 ID 必須是正整數");
        }

        // 防呆機制 2: 驗證範圍
        $scope = trim($scope);
        if (empty($scope)) {
            throw new FileServiceException("範圍不能為空");
        }

        // 防呆機制 3: 驗證使用者 ID
        if ($userId <= 0) {
            throw new FileServiceException("使用者 ID 必須是正整數");
        }

        $query = [
            'company_id' => $companyId,
            'scope' => $scope,
            'user_id' => $userId,
        ];

        return $this->httpClient->sendJsonRequest('GET', '/api/objects/', ['query' => $query]);
    }

    public function listObjects(int $userId, string $objectId): array
    {
        // 防呆機制 1: 驗證使用者 ID
        if ($userId <= 0) {
            throw new FileServiceException("使用者 ID 必須是正整數");
        }

        // 防呆機制 2: 驗證物件 ID
        $objectId = trim($objectId);
        if (empty($objectId)) {
            throw new FileServiceException("物件 ID 不能為空");
        }

        $query = [
            // 'company_id' => $companyId,
            // 'scope' => $scope,
            'user_id' => $userId,
        ];

        return $this->httpClient->sendJsonRequest('GET', "/api/objects/{$objectId}/list", ['query' => $query]);
    }


    
    /**
     * 建立物件資料陣列的輔助方法
     * @param int $company_id 公司 ID
     * @param string $scope 範圍
     * @param string $name 名稱
     * @param string $type 類型
     * @param string|null $parent 父物件 ID
     * @param string|null $storagePath 儲存路徑
     * @param string|null $mimeType 媒體類型
     * @param int|null $size 大小
     * @param string|null $sha1 SHA1 雜湊值
     * @param string|null $md5 MD5 雜湊值
     * @return array 解析後的資料
     */
    public function buildObjectData(
        int $company_id,
        string $scope,
        string $name,
        string $type,
        ?string $parent = null,
        ?string $storage_path = null,
        ?string $mimeType = null,
        ?int $size = null,
        ?string $sha1 = null,
        ?string $md5 = null
    ): array {

        if (
            empty($company_id) ||
            empty($scope) ||
            empty($name) ||
            empty($type) ||
            !in_array($type, ['file', 'folder'])
        ) {
            throw new FileServiceException("缺少必要參數");
        }

        $data = [
            'company_id' => $company_id,
            'scope' => $scope,
            'name' => $name,
            'type' => $type,
        ];

        if (!empty($parent)) {
            $data['parent'] = $parent;
        }

        if (!empty($storage_path)) {
            $data['storage_path'] = $storage_path;
        }

        if (!empty($mimeType)) {
            $data['mimeType'] = $mimeType;
        }

        if (!empty($size)) {
            $data['size'] = $size;
        }

        if (!empty($sha1)) {
            $data['sha1'] = $sha1;
        }

        if (!empty($md5)) {
            $data['md5'] = $md5;
        }

        return $data;
    }
    
    /**
     * 創建新物件（檔案或資料夾）
     * @param int $userId 使用者 ID
     * @param array $objectData 物件資料
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function createObject(int $userId, array $objectData): array
    {
        // 防呆機制 1: 驗證使用者 ID
        if ($userId <= 0) {
            throw new FileServiceException("使用者 ID 必須是正整數");
        }

        // 防呆機制 2: 驗證物件資料
        if (empty($objectData)) {
            throw new FileServiceException("objectData 不能為空");
        }

        // 防呆機制 3: 驗證必要欄位
        $requiredFields = ['company_id', 'scope', 'name', 'type'];
        foreach ($requiredFields as $field) {
            if (!isset($objectData[$field]) || empty(trim((string) $objectData[$field]))) {
                throw new FileServiceException("物件資料缺少必要欄位: {$field}");
            }
        }

        // 防呆機制 4: 驗證類型
        if (!in_array($objectData['type'], ['file', 'folder'])) {
            throw new FileServiceException("物件類型必須是 'file' 或 'folder'");
        }

        $query = ['user_id' => $userId];

        return $this->httpClient->sendJsonRequest('POST', '/api/objects/', [
            'query' => $query,
            'json' => $objectData
        ]);
    }

    /**
     * 刪除物件
     * @param string $objectId 物件 ID
     * @param int $userId 使用者 ID
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function deleteObject(string $objectId, int $userId): array
    {
        // 防呆機制 1: 驗證物件 ID
        $objectId = trim($objectId);
        if (empty($objectId)) {
            throw new FileServiceException("物件 ID 不能為空");
        }

        // 防呆機制 2: 驗證使用者 ID
        if ($userId <= 0) {
            throw new FileServiceException("使用者 ID 必須是正整數");
        }

        $query = ['user_id' => $userId];

        return $this->httpClient->sendJsonRequest('DELETE', "/api/objects/{$objectId}", [
            'query' => $query
        ]);
    }



    /**
     * 獲取物件資訊
     * @param string $objectId 物件 ID
     * @param int $userId 使用者 ID
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function getObjectInfo(string $objectId, int $userId): array
    {
        // 防呆機制 1: 驗證物件 ID
        $objectId = trim($objectId);
        if (empty($objectId)) {
            throw new FileServiceException("物件 ID 不能為空");
        }

        // 防呆機制 2: 驗證使用者 ID
        if ($userId <= 0) {
            throw new FileServiceException("使用者 ID 必須是正整數");
        }

        $query = ['user_id' => $userId];

        return $this->httpClient->sendJsonRequest('GET', "/api/objects/{$objectId}/metadata", [
            'query' => $query
        ]);
    }

    /**
     * 獲取物件下載資訊（專用於下載流程）
     * @param string $objectId 物件 ID
     * @param int $userId 使用者 ID
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function getDownloadInfo(string $objectId, int $userId): array
    {
        // 防呆機制 1: 驗證物件 ID
        $objectId = trim($objectId);
        if (empty($objectId)) {
            throw new FileServiceException("物件 ID 不能為空");
        }

        // 防呆機制 2: 驗證使用者 ID
        if ($userId <= 0) {
            throw new FileServiceException("使用者 ID 必須是正整數");
        }

        try {
            $query = ['user_id' => $userId];

            $response = $this->httpClient->sendJsonRequest('GET', "/api/objects/{$objectId}/download-info", [
                'query' => $query
            ]);

            // 驗證回應格式
            if (!isset($response['data'])) {
                throw new FileServiceException("下載資訊格式錯誤：缺少 data 欄位");
            }

            $data = $response['data'];

            // 驗證必要欄位
            $requiredFields = ['object_id', 'name', 'storage_type', 'full_path'];
            foreach ($requiredFields as $field) {
                if (!isset($data[$field])) {
                    throw new FileServiceException("下載資訊缺少必要欄位：{$field}");
                }
            }

            return $response;

        } catch (FileServiceException $e) {
            throw new FileServiceException("無法取得下載資訊: " . $e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * 檢查物件是否存在
     * @param string $objectId 物件 ID
     * @param int $userId 使用者 ID
     * @param string|null $versionId 版本 ID
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function checkObjectExists(string $objectId, int $userId, ?string $versionId = null): array
    {
        // 防呆機制 1: 驗證物件 ID
        $objectId = trim($objectId);
        if (empty($objectId)) {
            throw new FileServiceException("物件 ID 不能為空");
        }

        // 防呆機制 2: 驗證使用者 ID
        if ($userId <= 0) {
            throw new FileServiceException("使用者 ID 必須是正整數");
        }

        // 防呆機制 3: 驗證版本 ID（如果提供）
        if ($versionId !== null) {
            $versionId = trim($versionId);
            if (empty($versionId)) {
                throw new FileServiceException("版本 ID 不能為空");
            }
        }

        $query = ['user_id' => $userId];
        if ($versionId) {
            $query['version_id'] = $versionId;
        }

        return $this->httpClient->sendJsonRequest('GET', "/api/objects/{$objectId}/exists", [
            'query' => $query
        ]);
    }

    /**
     * 下載檔案
     * @param string $objectId 物件 ID
     * @param int $userId 使用者 ID
     * @return StreamInterface 下載的檔案串流
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function downloadObject(string $objectId, int $userId): StreamInterface
    {
        // 防呆機制 1: 驗證物件 ID
        $objectId = trim($objectId);
        if (empty($objectId)) {
            throw new FileServiceException("物件 ID 不能為空");
        }

        // 防呆機制 2: 驗證使用者 ID
        if ($userId <= 0) {
            throw new FileServiceException("使用者 ID 必須是正整數");
        }

        // 步驟 1: 獲取下載資訊
        try {
            $downloadInfo = $this->getDownloadInfo($objectId, $userId);
        } catch (FileServiceException $e) {
            throw new FileServiceException("無法取得下載資訊: " . $e->getMessage(), $e->getCode(), $e);
        }

        $downloadData = $downloadInfo['data'];

        // 步驟 2: 根據儲存類型決定下載策略
        switch (strtoupper($downloadData['storage_type'])) {
            case 'AP_LOCAL':
                return $this->downloadFromApLocal($downloadData);

            case 'FS_LOCAL':
            case 'S3':
            case 'FTP':
            case 'NAS':
            default:
                return $this->downloadFromRemote($objectId, $userId);
        }
    }

    /**
     * 從 AP_LOCAL 儲存下載檔案
     * @param array $downloadData 下載資料（來自 download-info API）
     * @return StreamInterface 檔案串流
     * @throws FileServiceException 下載失敗時拋出例外
     */
    private function downloadFromApLocal(array $downloadData): StreamInterface
    {
        // 使用 download-info 提供的完整路徑
        if (!isset($downloadData['full_path']) || empty($downloadData['full_path'])) {
            throw new FileServiceException("AP_LOCAL 儲存類型缺少 full_path 資訊");
        }

        $fullPath = $downloadData['full_path'];
        $fileName = $downloadData['name'] ?? 'unknown';

        // 安全性檢查：防止路徑遍歷攻擊
        if (strpos($fullPath, '..') !== false) {
            throw new FileServiceException("檔案路徑包含不安全字符: {$fullPath}");
        }

        // 檢查檔案是否存在
        if (!file_exists($fullPath)) {
            throw new FileServiceException("本地檔案不存在: {$fullPath}");
        }

        // 檢查檔案是否可讀
        if (!is_readable($fullPath)) {
            throw new FileServiceException("本地檔案無法讀取: {$fullPath}");
        }

        // 檢查是否為檔案（不是目錄）
        if (!is_file($fullPath)) {
            throw new FileServiceException("指定路徑不是有效檔案: {$fullPath}");
        }

        // 可選：檔案大小檢查
        if (isset($downloadData['size']) && $downloadData['size'] !== null) {
            $actualSize = filesize($fullPath);
            if ($actualSize !== $downloadData['size']) {
                throw new FileServiceException("檔案大小不符：預期 {$downloadData['size']} 位元組，實際 {$actualSize} 位元組");
            }
        }

        try {
            // 建立檔案串流（使用二進位模式）
            $fileHandle = fopen($fullPath, 'rb');
            if ($fileHandle === false) {
                throw new FileServiceException("無法開啟本地檔案: {$fullPath}");
            }

            // 對於大檔案，設置緩衝區
            $fileSize = filesize($fullPath);
            if ($fileSize > 50 * 1024 * 1024) { // 50MB
                stream_set_read_buffer($fileHandle, 8192); // 8KB 緩衝區
            }

            // 建立 PSR-7 串流
            $stream = \GuzzleHttp\Psr7\Utils::streamFor($fileHandle);

            // 可選：設置串流元數據
            if (isset($downloadData['mime_type'])) {
                $stream = $stream->withMetadata('mediaType', $downloadData['mime_type']);
            }

            return $stream;

        } catch (\Exception $e) {
            // 確保檔案控制代碼被正確關閉
            if (isset($fileHandle) && is_resource($fileHandle)) {
                fclose($fileHandle);
            }

            if ($e instanceof FileServiceException) {
                throw $e;
            }

            throw new FileServiceException("本地檔案處理失敗: " . $e->getMessage(), 0, $e);
        }
    }

    /**
     * 從遠端儲存下載檔案
     * @param string $objectId 物件 ID
     * @param int $userId 使用者 ID
     * @return StreamInterface 檔案串流
     * @throws FileServiceException 下載失敗時拋出例外
     */
    private function downloadFromRemote(string $objectId, int $userId): StreamInterface
    {
        try {
            $query = ['user_id' => $userId];

            // 發送下載請求到 file-service
            $response = $this->httpClient->sendRawRequest('GET', "/api/objects/{$objectId}/download", [
                'query' => $query,
                'stream' => true, // 啟用串流模式
                'timeout' => 300, // 5分鐘超時，適合大檔案
            ]);

            // 檢查回應狀態
            if ($response->getStatusCode() !== 200) {
                $body = $response->getBody()->getContents();
                throw new FileServiceException("遠端下載失敗，狀態碼: " . $response->getStatusCode() . ", 內容: " . $body);
            }

            // 檢查是否為 AP_LOCAL 的特殊回應
            $contentType = $response->getHeaderLine('Content-Type');
            if ($contentType === 'application/json') {
                // 可能是 AP_LOCAL 的 JSON 回應，嘗試解析
                $body = $response->getBody()->getContents();
                $jsonData = json_decode($body, true);

                if (isset($jsonData['message']) && $jsonData['message'] === 'AP_LOCAL_FILE') {
                    // 這是 AP_LOCAL 檔案，但不應該到這裡，因為應該在 downloadObject 中處理
                    throw new FileServiceException("AP_LOCAL 檔案應該由客戶端直接處理，不應該調用遠端下載");
                }

                if (isset($jsonData['message'])) {
                    throw new FileServiceException("遠端下載失敗: " . $jsonData['message']);
                }
            }

            return $response->getBody();

        } catch (FileServiceException $e) {
            throw new FileServiceException("遠端檔案下載失敗: " . $e->getMessage(), $e->getCode(), $e);
        } catch (\Exception $e) {
            throw new FileServiceException("下載過程發生未預期錯誤: " . $e->getMessage(), 0, $e);
        }
    }

    /**
     * 重命名物件
     * @param string $objectId 物件 ID
     * @param int $userId 使用者 ID
     * @param string $newName 新名稱
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function renameObject(string $objectId, int $userId, string $newName): array
    {
        // 防呆機制 1: 驗證物件 ID
        $objectId = trim($objectId);
        if (empty($objectId)) {
            throw new FileServiceException("物件 ID 不能為空");
        }

        // 防呆機制 2: 驗證使用者 ID
        if ($userId <= 0) {
            throw new FileServiceException("使用者 ID 必須是正整數");
        }

        // 防呆機制 3: 驗證新名稱
        $newName = trim($newName);
        if (empty($newName)) {
            throw new FileServiceException("新名稱不能為空");
        }

        // 防呆機制 4: 檢查名稱長度
        if (strlen($newName) > 255) {
            throw new FileServiceException("檔案名稱不能超過 255 個字元");
        }

        // 防呆機制 5: 檢查非法字元
        $invalidChars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|'];
        foreach ($invalidChars as $char) {
            if (strpos($newName, $char) !== false) {
                throw new FileServiceException("檔案名稱包含非法字元: {$char}");
            }
        }

        $query = ['user_id' => $userId];

        return $this->httpClient->sendJsonRequest('PATCH', "/api/objects/{$objectId}/rename", [
            'query' => $query,
            'json' => ['new_name' => $newName]
        ]);
    }

    /**
     * 複製物件
     * @param string $objectId 物件 ID
     * @param int $userId 使用者 ID
     * @param string $targetId 目標 ID
     * @param string|null $newName 新名稱
     * @param string|null $duplicateStrategy 重複檔案處理策略，ignore|overwrite
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function copyObjects(string $objectId, int $userId, string $targetId, ?string $newName = null, ?string $duplicateStrategy = null): array
    {
        // 防呆機制 1: 驗證物件 ID
        $objectId = trim($objectId);
        if (empty($objectId)) {
            throw new FileServiceException("物件 ID 不能為空");
        }

        // 防呆機制 2: 驗證使用者 ID
        if ($userId <= 0) {
            throw new FileServiceException("使用者 ID 必須是正整數");
        }

        // 防呆機制 3: 驗證目標 ID
        $targetId = trim($targetId);
        if (empty($targetId)) {
            throw new FileServiceException("目標 ID 不能為空");
        }

        // 防呆機制 4: 檢查是否嘗試複製到自己
        if ($objectId === $targetId) {
            throw new FileServiceException("不能將物件複製到自己");
        }

        $query = ['user_id' => $userId];
        $data = ['targetId' => $targetId];

        if ($newName) {
            // 防呆機制 5: 驗證新名稱
            $newName = trim($newName);
            if (empty($newName)) {
                throw new FileServiceException("新名稱不能為空字串");
            }

            // 防呆機制 6: 檢查名稱長度
            if (strlen($newName) > 255) {
                throw new FileServiceException("檔案名稱不能超過 255 個字元");
            }

            $data['newName'] = $newName;
        }

        if ($duplicateStrategy) {
            $data['duplicateStrategy'] = $duplicateStrategy;
        }

        return $this->httpClient->sendJsonRequest('POST', "/api/objects/{$objectId}/copy", [
            'query' => $query,
            'json' => $data
        ]);
    }

    /**
     * 移動物件
     * @param string $objectId 物件 ID
     * @param int $userId 使用者 ID
     * @param string $targetId 目標 ID
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function moveObjects(string $objectId, int $userId, string $targetId, ?string $duplicateStrategy = null): array
    {
        // 防呆機制 1: 驗證物件 ID
        $objectId = trim($objectId);
        if (empty($objectId)) {
            throw new FileServiceException("物件 ID 不能為空");
        }

        // 防呆機制 2: 驗證使用者 ID
        if ($userId <= 0) {
            throw new FileServiceException("使用者 ID 必須是正整數");
        }

        // 防呆機制 3: 驗證目標 ID
        $targetId = trim($targetId);
        if (empty($targetId)) {
            throw new FileServiceException("目標 ID 不能為空");
        }

        // 防呆機制 4: 檢查是否嘗試移動到自己
        if ($objectId === $targetId) {
            throw new FileServiceException("不能將物件移動到自己");
        }

        $query = ['user_id' => $userId];
        $data = ['targetId' => $targetId];

        if ($duplicateStrategy) {
            $data['duplicateStrategy'] = $duplicateStrategy;
        }

        return $this->httpClient->sendJsonRequest('POST', "/api/objects/{$objectId}/move", [
            'query' => $query,
            'json' => $data
        ]);
    }


    /**
     * 上傳檔案（從檔案路徑）
     * @param int $userId 使用者 ID
     * @param string $filePath 本地檔案路徑
     * @param string $objectId 物件 ID
     * @param callable|null $progressCallback 進度回調函數
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function uploadFile(int $userId, string $filePath, string $objectId, ?callable $progressCallback = null): array
    {
        // 防呆機制 1: 驗證使用者 ID
        if ($userId <= 0) {
            throw new FileServiceException("使用者 ID 必須是正整數");
        }

        // 防呆機制 2: 驗證檔案路徑
        $filePath = trim($filePath);
        if (empty($filePath)) {
            throw new FileServiceException("檔案路徑不能為空");
        }

        // 防呆機制 3: 檢查檔案是否存在
        if (!file_exists($filePath)) {
            throw new FileServiceException("檔案不存在: {$filePath}");
        }

        // 防呆機制 4: 檢查檔案是否可讀
        if (!is_readable($filePath)) {
            throw new FileServiceException("檔案無法讀取: {$filePath}");
        }

        // 防呆機制 5: 檢查是否為檔案（不是目錄）
        if (!is_file($filePath)) {
            throw new FileServiceException("路徑不是有效的檔案: {$filePath}");
        }

        // 防呆機制 6: 驗證物件 ID
        $objectId = trim($objectId);
        if (empty($objectId)) {
            throw new FileServiceException("物件 ID 不能為空");
        }

        // 防呆機制 7: 檢查檔案大小
        $fileSize = filesize($filePath);
        if ($fileSize === false) {
            throw new FileServiceException("無法取得檔案大小: {$filePath}");
        }

        if (empty($fileContent)) {
            throw new FileServiceException("檔案內容不能為空");
        }

        $query = ['user_id' => $userId];

        $options = [
            'query' => $query,
            'multipart' => [
                [
                    'Content-type' => 'multipart/form-data',
                    'name' => 'file',
                    'contents' => fopen($filePath, 'r'),
                    'filename' => basename($filePath)
                ],
                [
                    'name' => 'object_id',
                    'contents' => $objectId,
                    // 'headers' => ['Content-Type' => 'application/json']
                ]
            ]
        ];

        // 添加進度回調
        if ($progressCallback) {
            // 防呆機制 10: 驗證回調函數
            if (!is_callable($progressCallback)) {
                throw new FileServiceException("進度回調必須是可呼叫的函數");
            }
            $options['progress'] = $progressCallback;
        }

        // 執行上傳
        $result = $this->httpClient->sendMultipartRequest('/api/objects/upload', $options);

        // 如果上傳成功且需要重命名本地檔案
        if (isset($result['success']) && $result['success']) {
            $directory = dirname($filePath);
            $newFilePath = $directory . DIRECTORY_SEPARATOR . pathinfo($filePath, PATHINFO_FILENAME) . "_uploaded" . pathinfo($filePath, PATHINFO_EXTENSION);


            // 嘗試重命名本地檔案
            rename($filePath, $newFilePath);
        }

        return $result;
    }

    /**
     * 上傳檔案（從檔案內容）
     * @param int $userId 使用者 ID
     * @param string $fileContent 檔案內容
     * @param string $fileName 檔案名稱
     * @param string $objectId 物件 ID
     * @param callable|null $progressCallback 進度回調函數
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function uploadFileFromContent(int $userId, string $fileContent, string $fileName, string $objectId, ?callable $progressCallback = null): array
    {
        // 防呆機制 1: 驗證使用者 ID
        if ($userId <= 0) {
            throw new FileServiceException("使用者 ID 必須是正整數");
        }

        // 防呆機制 2: 驗證檔案內容
        if (empty($fileContent)) {
            throw new FileServiceException("檔案內容不能為空");
        }

        // 防呆機制 3: 檢查檔案內容大小
        $contentSize = strlen($fileContent);
        $maxFileSize = 100 * 1024 * 1024; // 100MB
        if ($contentSize > $maxFileSize) {
            throw new FileServiceException("檔案內容大小超過限制 (100MB): " . number_format($contentSize / 1024 / 1024, 2) . "MB");
        }

        // 防呆機制 4: 驗證檔案名稱
        $fileName = trim($fileName);
        if (empty($fileName)) {
            throw new FileServiceException("檔案名稱不能為空");
        }

        // 防呆機制 5: 檢查檔案名稱長度
        if (strlen($fileName) > 255) {
            throw new FileServiceException("檔案名稱不能超過 255 個字元");
        }

        // 防呆機制 6: 驗證物件 ID
        $objectId = trim($objectId);
        if (empty($objectId)) {
            throw new FileServiceException("物件 ID 不能為空");
        }

        $query = ['user_id' => $userId];

        $options = [
            'query' => $query,
            'multipart' => [
                [
                    'name' => 'file',
                    'contents' => $fileContent,
                    'filename' => $fileName
                ],
                [
                    'name' => 'object_id',
                    'contents' => $objectId,
                    'headers' => ['Content-Type' => 'application/json']
                ]
            ]
        ];

        // 添加進度回調
        if ($progressCallback) {
            // 防呆機制 7: 驗證回調函數
            if (!is_callable($progressCallback)) {
                throw new FileServiceException("進度回調必須是可呼叫的函數");
            }
            $options['progress'] = $progressCallback;
        }

        return $this->httpClient->sendJsonRequest('POST', '/api/objects/upload', $options);
    }

    /**
     * 上傳檔案（從 Stream）
     * @param int $userId 使用者 ID
     * @param resource $stream 檔案串流
     * @param string $fileName 檔案名稱
     * @param string $objectId 物件 ID
     * @param callable|null $progressCallback 進度回調函數
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function uploadFileFromStream(int $userId, $stream, string $fileName, string $objectId, ?callable $progressCallback = null): array
    {
        // 防呆機制 1: 驗證使用者 ID
        if ($userId <= 0) {
            throw new FileServiceException("使用者 ID 必須是正整數");
        }

        // 防呆機制 2: 驗證串流資源
        if (!is_resource($stream)) {
            throw new FileServiceException("提供的不是有效的資源串流");
        }

        // 防呆機制 3: 檢查串流類型
        $streamType = get_resource_type($stream);
        if ($streamType !== 'stream') {
            throw new FileServiceException("提供的資源不是有效的串流類型: {$streamType}");
        }

        // 防呆機制 4: 驗證檔案名稱
        $fileName = trim($fileName);
        if (empty($fileName)) {
            throw new FileServiceException("檔案名稱不能為空");
        }

        // 防呆機制 5: 檢查檔案名稱長度
        if (strlen($fileName) > 255) {
            throw new FileServiceException("檔案名稱不能超過 255 個字元");
        }

        // 防呆機制 6: 驗證物件 ID
        $objectId = trim($objectId);
        if (empty($objectId)) {
            throw new FileServiceException("物件 ID 不能為空");
        }

        $query = ['user_id' => $userId];

        $options = [
            'query' => $query,
            'multipart' => [
                [
                    'name' => 'file',
                    'contents' => $stream,
                    'filename' => $fileName
                ],
                [
                    'name' => 'object_id',
                    'contents' => $objectId,
                    'headers' => ['Content-Type' => 'application/json']
                ]
            ]
        ];

        // 添加進度回調
        if ($progressCallback) {
            // 防呆機制 7: 驗證回調函數
            if (!is_callable($progressCallback)) {
                throw new FileServiceException("進度回調必須是可呼叫的函數");
            }
            $options['progress'] = $progressCallback;
        }

        return $this->httpClient->sendJsonRequest('POST', '/api/objects/upload', $options);
    }


    /**
     * 搜尋檔案和資料夾
     * @param int $companyId 公司 ID
     * @param string $scope 範圍
     * @param int $userId 使用者 ID
     * @param string|null $query 搜尋查詢
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function searchObjects(int $companyId, string $scope, int $userId, string $query): array
    {
        // 防呆機制 1: 驗證公司 ID
        if ($companyId <= 0) {
            throw new FileServiceException("公司 ID 必須是正整數");
        }

        // 防呆機制 2: 驗證範圍
        $scope = trim($scope);
        if (empty($scope)) {
            throw new FileServiceException("範圍不能為空");
        }

        // 防呆機制 3: 驗證使用者 ID
        if ($userId <= 0) {
            throw new FileServiceException("使用者 ID 必須是正整數");
        }

        // 防呆機制 4: 驗證搜尋查詢（如果提供）
        $query = trim($query);
        if (empty($query)) {
            throw new FileServiceException("搜尋查詢不能為空字串");
        }


        $queryParams = [
            'company_id' => $companyId,
            'scope' => $scope,
            'user_id' => $userId,
            'query' => $query
        ];

        return $this->httpClient->sendJsonRequest('GET', '/api/search', [
            'query' => $queryParams
        ]);
    }

    /**
     * 獲取版本列表
     * @param string $objectId 物件 ID
     * @param int $userId 使用者 ID
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function getVersionList(string $objectId, int $userId): array
    {
        // 防呆機制 1: 驗證物件 ID
        $objectId = trim($objectId);
        if (empty($objectId)) {
            throw new FileServiceException("物件 ID 不能為空");
        }

        // 防呆機制 2: 驗證使用者 ID
        if ($userId <= 0) {
            throw new FileServiceException("使用者 ID 必須是正整數");
        }

        $query = ['user_id' => $userId];

        return $this->httpClient->sendJsonRequest('GET', "/api/objects/{$objectId}/versions", [
            'query' => $query
        ]);
    }

    /**
     * 獲取物件歷史版本資訊
     * @param string $objectId 物件 ID
     * @param string $versionId 版本 ID
     * @param int $userId 使用者 ID
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function getObjectVersionInfo(string $objectId, string $versionId, int $userId): array
    {
        // 防呆機制 1: 驗證物件 ID
        $objectId = trim($objectId);
        if (empty($objectId)) {
            throw new FileServiceException("物件 ID 不能為空");
        }

        // 防呆機制 2: 驗證版本 ID
        $versionId = trim($versionId);
        if (empty($versionId)) {
            throw new FileServiceException("版本 ID 不能為空");
        }

        // 防呆機制 3: 驗證使用者 ID
        if ($userId <= 0) {
            throw new FileServiceException("使用者 ID 必須是正整數");
        }

        $query = ['user_id' => $userId];

        return $this->httpClient->sendJsonRequest('GET', "/api/objects/{$objectId}/versions/{$versionId}", [
            'query' => $query
        ]);
    }

    /**
     * 下載歷史檔案
     * @param string $objectId 物件 ID
     * @param string $versionId 版本 ID
     * @param int $userId 使用者 ID
     * @return StreamInterface 下載的檔案內容
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function downloadObjectVersion(string $objectId, string $versionId, int $userId): StreamInterface
    {
        // 防呆機制 1: 驗證物件 ID
        $objectId = trim($objectId);
        if (empty($objectId)) {
            throw new FileServiceException("物件 ID 不能為空");
        }

        // 防呆機制 2: 驗證版本 ID
        $versionId = trim($versionId);
        if (empty($versionId)) {
            throw new FileServiceException("版本 ID 不能為空");
        }

        // 防呆機制 3: 驗證使用者 ID
        if ($userId <= 0) {
            throw new FileServiceException("使用者 ID 必須是正整數");
        }

        $query = ['user_id' => $userId];

        $response = $this->httpClient->sendRawRequest('GET', "/api/objects/{$objectId}/versions/{$versionId}/download", [
            'query' => $query
        ]);

        return $response->getBody();
    }

    /**
     * 還原特定版本
     * @param string $objectId 物件 ID
     * @param string $versionId 版本 ID
     * @param int $userId 使用者 ID
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function restoreVersion(string $objectId, string $versionId, int $userId): array
    {
        // 防呆機制 1: 驗證物件 ID
        $objectId = trim($objectId);
        if (empty($objectId)) {
            throw new FileServiceException("物件 ID 不能為空");
        }

        // 防呆機制 2: 驗證版本 ID
        $versionId = trim($versionId);
        if (empty($versionId)) {
            throw new FileServiceException("版本 ID 不能為空");
        }

        // 防呆機制 3: 驗證使用者 ID
        if ($userId <= 0) {
            throw new FileServiceException("使用者 ID 必須是正整數");
        }

        $query = ['user_id' => $userId];

        return $this->httpClient->sendJsonRequest('POST', "/api/objects/{$objectId}/versions/{$versionId}/restore", [
            'query' => $query
        ]);
    }

}