<?php

declare(strict_types=1);

namespace FDMC\FileServiceClient;

use InvalidArgumentException;

/**
 * 配置發布器
 * 
 * 提供配置檔案發布功能，支援 Laravel 和原生 PHP 專案
 * 
 * 功能包括：
 * - Laravel 專案配置發布
 * - 原生 PHP 專案配置複製
 * - 配置目錄自動建立
 * - 配置檔案覆蓋確認機制
 */
class ConfigPublisher
{
    /**
     * 套件預設配置檔案路徑
     */
    private string $defaultConfigPath;
    
    /**
     * 配置記錄器
     */
    private ?ConfigLogger $logger;
    
    /**
     * 輸入處理器（用於測試注入）
     */
    private ?\Closure $inputHandler = null;
    
    public function __construct(?ConfigLogger $logger = null, ?string $defaultConfigPath = null)
    {
        $this->defaultConfigPath = $defaultConfigPath ?? $this->getDefaultConfigPath();
        $this->logger = $logger;
    }
    
    /**
     * 設定輸入處理器（主要用於測試）
     * 
     * @param \Closure $handler 輸入處理器
     */
    public function setInputHandler(\Closure $handler): void
    {
        $this->inputHandler = $handler;
    }
    
    /**
     * 發布配置到 Laravel 專案
     * 
     * @param string|null $targetPath 目標路徑，預設為 config/file-service.php
     * @param bool $force 是否強制覆蓋現有檔案
     * @return bool 發布是否成功
     * @throws ConfigurationException 當發布失敗時
     */
    public function publishToLaravel(?string $targetPath = null, bool $force = false): bool
    {
        // 使用 Laravel 的 config_path 函數獲取目標路徑
        if ($targetPath === null) {
            if (!function_exists('config_path')) {
                throw ConfigurationException::publishFailed(
                    'config_path 函數不存在，請確認在 Laravel 環境中執行'
                );
            }
            $targetPath = config_path('file-service.php');
        }
        
        return $this->publishConfigFile($targetPath, $force, 'Laravel');
    }
    
    /**
     * 發布配置到原生 PHP 專案
     * 
     * @param string|null $targetPath 目標路徑，預設為 config/file-service.php
     * @param bool $force 是否強制覆蓋現有檔案
     * @return bool 發布是否成功
     * @throws ConfigurationException 當發布失敗時
     */
    public function publishToNative(?string $targetPath = null, bool $force = false): bool
    {
        if ($targetPath === null) {
            // 尋找專案根目錄
            $projectRoot = $this->findProjectRoot();
            $targetPath = $projectRoot . DIRECTORY_SEPARATOR . 'config' . DIRECTORY_SEPARATOR . 'file-service.php';
        }
        
        return $this->publishConfigFile($targetPath, $force, '原生 PHP');
    }
    
    /**
     * 複製配置檔案到指定位置
     * 
     * @param string $targetPath 目標檔案路徑
     * @param bool $force 是否強制覆蓋現有檔案
     * @param string $environment 環境類型（用於日誌）
     * @return bool 複製是否成功
     * @throws ConfigurationException 當複製失敗時
     */
    public function publishConfigFile(string $targetPath, bool $force = false, string $environment = ''): bool
    {
        // 驗證來源檔案
        if (!file_exists($this->defaultConfigPath)) {
            throw ConfigurationException::publishFailed(
                "套件預設配置檔案不存在: {$this->defaultConfigPath}"
            );
        }
        
        if (!is_readable($this->defaultConfigPath)) {
            throw ConfigurationException::publishFailed(
                "套件預設配置檔案無法讀取: {$this->defaultConfigPath}"
            );
        }
        
        // 檢查目標檔案是否存在
        if (file_exists($targetPath) && !$force) {
            if (!$this->confirmOverwrite($targetPath)) {
                $this->log("取消發布配置檔案到: {$targetPath}", ConfigLogger::LEVEL_INFO);
                return false;
            }
        }
        
        // 建立目標目錄
        $targetDir = dirname($targetPath);
        if (!$this->createConfigDirectory($targetDir)) {
            throw ConfigurationException::publishFailed(
                "無法建立配置目錄: {$targetDir}"
            );
        }
        
        // 複製檔案
        if (!copy($this->defaultConfigPath, $targetPath)) {
            throw ConfigurationException::publishFailed(
                "無法複製配置檔案從 {$this->defaultConfigPath} 到 {$targetPath}"
            );
        }
        
        // 設定檔案權限
        if (!chmod($targetPath, 0644)) {
            $this->log("警告：無法設定配置檔案權限: {$targetPath}", ConfigLogger::LEVEL_WARNING);
        }
        
        $envText = $environment ? "({$environment}) " : '';
        $this->log("成功發布配置檔案 {$envText}到: {$targetPath}", ConfigLogger::LEVEL_INFO);
        
        return true;
    }
    
    /**
     * 建立配置目錄
     * 
     * @param string $directory 目錄路徑
     * @return bool 建立是否成功
     */
    public function createConfigDirectory(string $directory): bool
    {
        if (empty($directory)) {
            return false;
        }
        
        // 如果目錄已存在，檢查是否可寫
        if (is_dir($directory)) {
            if (!is_writable($directory)) {
                $this->log("配置目錄不可寫: {$directory}", ConfigLogger::LEVEL_ERROR);
                return false;
            }
            return true;
        }
        
        // 建立目錄（遞迴建立）
        try {
            if (!mkdir($directory, 0755, true)) {
                $this->log("無法建立配置目錄: {$directory}", ConfigLogger::LEVEL_ERROR);
                return false;
            }
        } catch (\Exception $e) {
            $this->log("建立配置目錄時發生錯誤: {$directory} - " . $e->getMessage(), ConfigLogger::LEVEL_ERROR);
            return false;
        }
        
        $this->log("成功建立配置目錄: {$directory}", ConfigLogger::LEVEL_INFO);
        return true;
    }
    
    /**
     * 確認是否覆蓋現有檔案
     * 
     * @param string $filePath 檔案路徑
     * @return bool 是否確認覆蓋
     */
    private function confirmOverwrite(string $filePath): bool
    {
        // 在非互動環境中，預設不覆蓋
        if (!$this->isInteractiveEnvironment()) {
            $this->log("配置檔案已存在，非互動環境中不覆蓋: {$filePath}", ConfigLogger::LEVEL_WARNING);
            return false;
        }
        
        // 如果有自訂輸入處理器（測試用），使用它
        if ($this->inputHandler) {
            return ($this->inputHandler)($filePath);
        }
        
        // 顯示確認訊息
        echo "配置檔案已存在: {$filePath}\n";
        echo "是否要覆蓋現有檔案？ [y/N]: ";
        
        $handle = fopen('php://stdin', 'r');
        if (!$handle) {
            $this->log("無法讀取使用者輸入，取消覆蓋", ConfigLogger::LEVEL_WARNING);
            return false;
        }
        
        $input = trim(fgets($handle));
        fclose($handle);
        
        $confirmed = in_array(strtolower($input), ['y', 'yes', '是'], true);
        
        if ($confirmed) {
            $this->log("使用者確認覆蓋配置檔案: {$filePath}", ConfigLogger::LEVEL_INFO);
        } else {
            $this->log("使用者取消覆蓋配置檔案: {$filePath}", ConfigLogger::LEVEL_INFO);
        }
        
        return $confirmed;
    }
    
    /**
     * 檢查是否為互動環境
     * 
     * @return bool 是否為互動環境
     */
    private function isInteractiveEnvironment(): bool
    {
        // 檢查是否在命令列中執行且有 TTY
        return php_sapi_name() === 'cli' && stream_isatty(STDIN);
    }
    
    /**
     * 尋找專案根目錄
     * 
     * 透過尋找 composer.json 檔案來確定專案根目錄
     * 
     * @return string 專案根目錄路徑
     * @throws ConfigurationException 當找不到專案根目錄時
     */
    private function findProjectRoot(): string
    {
        $currentDir = getcwd();
        if (!$currentDir) {
            throw ConfigurationException::publishFailed('無法獲取當前工作目錄');
        }
        
        $searchPaths = [
            $currentDir,
            dirname($currentDir),
            dirname(dirname($currentDir)),
        ];
        
        // 如果有 $_SERVER['SCRIPT_FILENAME']，也加入搜尋路徑
        if (isset($_SERVER['SCRIPT_FILENAME'])) {
            $scriptDir = dirname($_SERVER['SCRIPT_FILENAME']);
            $searchPaths[] = $scriptDir;
            $searchPaths[] = dirname($scriptDir);
        }
        
        foreach ($searchPaths as $path) {
            $composerPath = $path . DIRECTORY_SEPARATOR . 'composer.json';
            if (file_exists($composerPath)) {
                $this->log("找到專案根目錄: {$path}", ConfigLogger::LEVEL_INFO);
                return $path;
            }
        }
        
        // 如果找不到 composer.json，使用當前目錄
        $this->log("未找到 composer.json，使用當前目錄作為專案根目錄: {$currentDir}", ConfigLogger::LEVEL_WARNING);
        return $currentDir;
    }
    
    /**
     * 獲取套件預設配置檔案路徑
     * 
     * @return string 預設配置檔案路徑
     */
    private function getDefaultConfigPath(): string
    {
        return __DIR__ . '/../config/file-service.php';
    }
    
    /**
     * 記錄訊息
     * 
     * @param string $message 訊息內容
     * @param int $level 日誌等級
     */
    private function log(string $message, int $level = ConfigLogger::LEVEL_INFO): void
    {
        if ($this->logger) {
            $this->logger->log($level, "[ConfigPublisher] {$message}");
        }
    }
    
    /**
     * 獲取發布狀態資訊
     * 
     * @param string|null $targetPath 目標路徑
     * @return array 發布狀態資訊
     */
    public function getPublishStatus(?string $targetPath = null): array
    {
        if ($targetPath === null) {
            // 嘗試偵測環境並獲取預設路徑
            if (function_exists('config_path')) {
                $targetPath = config_path('file-service.php');
                $environment = 'Laravel';
            } else {
                try {
                    $projectRoot = $this->findProjectRoot();
                    $targetPath = $projectRoot . DIRECTORY_SEPARATOR . 'config' . DIRECTORY_SEPARATOR . 'file-service.php';
                    $environment = '原生 PHP';
                } catch (ConfigurationException $e) {
                    return [
                        'published' => false,
                        'error' => $e->getMessage(),
                    ];
                }
            }
        } else {
            $environment = '自訂路徑';
        }
        
        $published = file_exists($targetPath);
        $result = [
            'published' => $published,
            'target_path' => $targetPath,
            'environment' => $environment,
        ];
        
        if ($published) {
            $result['file_size'] = filesize($targetPath);
            $result['modified_time'] = filemtime($targetPath);
            $result['readable'] = is_readable($targetPath);
            $result['writable'] = is_writable($targetPath);
        }
        
        return $result;
    }
}