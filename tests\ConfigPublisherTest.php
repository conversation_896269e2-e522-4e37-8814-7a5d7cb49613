<?php

declare(strict_types=1);

namespace FDMC\FileServiceClient\Tests;

use PHPUnit\Framework\TestCase;
use FDMC\FileServiceClient\ConfigPublisher;
use FDMC\FileServiceClient\ConfigLogger;
use FDMC\FileServiceClient\ConfigurationException;

// 定義 env 函數（如果不存在）
if (!function_exists('env')) {
    function env($key, $default = null) {
        return $_ENV[$key] ?? getenv($key) ?: $default;
    }
}

/**
 * ConfigPublisher 測試
 */
class ConfigPublisherTest extends TestCase
{
    private ConfigPublisher $publisher;
    private string $testDir;
    private string $testConfigPath;
    
    protected function setUp(): void
    {
        parent::setUp();
        

        
        // 建立測試目錄
        $this->testDir = sys_get_temp_dir() . '/file-service-test-' . uniqid();
        mkdir($this->testDir, 0755, true);
        
        $this->testConfigPath = $this->testDir . '/config/file-service.php';
        
        // 建立配置發布器，使用測試專用的配置檔案
        $logger = $this->createMock(ConfigLogger::class);
        $testConfigPath = __DIR__ . '/fixtures/test-config.php';
        $this->publisher = new ConfigPublisher($logger, $testConfigPath);
    }
    
    protected function tearDown(): void
    {
        // 清理測試檔案
        $this->removeDirectory($this->testDir);
        parent::tearDown();
    }
    
    /**
     * 測試發布到原生 PHP 專案
     */
    public function testPublishToNative(): void
    {
        // 測試發布
        $success = $this->publisher->publishToNative($this->testConfigPath, false);
        
        $this->assertTrue($success);
        $this->assertFileExists($this->testConfigPath);
        $this->assertIsReadable($this->testConfigPath);
        
        // 驗證配置檔案內容
        $config = require $this->testConfigPath;
        $this->assertIsArray($config);
        $this->assertArrayHasKey('baseUrl', $config);
        $this->assertArrayHasKey('http', $config);
    }
    
    /**
     * 測試強制覆蓋現有檔案
     */
    public function testPublishWithForceOverwrite(): void
    {
        // 先建立一個測試檔案
        $configDir = dirname($this->testConfigPath);
        mkdir($configDir, 0755, true);
        file_put_contents($this->testConfigPath, '<?php return ["test" => true];');
        
        $originalContent = file_get_contents($this->testConfigPath);
        
        // 強制覆蓋
        $success = $this->publisher->publishToNative($this->testConfigPath, true);
        
        $this->assertTrue($success);
        $this->assertFileExists($this->testConfigPath);
        
        $newContent = file_get_contents($this->testConfigPath);
        $this->assertNotEquals($originalContent, $newContent);
        
        // 驗證新內容是正確的配置
        $config = require $this->testConfigPath;
        $this->assertIsArray($config);
        $this->assertArrayHasKey('baseUrl', $config);
    }
    
    /**
     * 測試不覆蓋現有檔案（使用者選擇不覆蓋）
     */
    public function testPublishWithoutOverwriteInNonInteractiveEnvironment(): void
    {
        // 先建立一個測試檔案
        $configDir = dirname($this->testConfigPath);
        mkdir($configDir, 0755, true);
        $originalContent = '<?php return ["test" => true];';
        file_put_contents($this->testConfigPath, $originalContent);
        
        // 設定輸入處理器返回 false（模擬使用者選擇不覆蓋）
        $this->publisher->setInputHandler(function($filePath) {
            return false; // 不覆蓋
        });
        
        // 嘗試發布（不強制覆蓋）
        $success = $this->publisher->publishToNative($this->testConfigPath, false);
        
        // 應該返回 false（不覆蓋）
        $this->assertFalse($success);
        
        // 檔案內容應該保持不變
        $currentContent = file_get_contents($this->testConfigPath);
        $this->assertEquals($originalContent, $currentContent);
    }
    
    /**
     * 測試使用者確認覆蓋現有檔案
     */
    public function testPublishWithUserConfirmation(): void
    {
        // 先建立一個測試檔案
        $configDir = dirname($this->testConfigPath);
        mkdir($configDir, 0755, true);
        $originalContent = '<?php return ["test" => true];';
        file_put_contents($this->testConfigPath, $originalContent);
        
        // 設定輸入處理器返回 true（模擬使用者選擇覆蓋）
        $this->publisher->setInputHandler(function($filePath) {
            return true; // 覆蓋
        });
        
        // 嘗試發布（不強制覆蓋，但使用者確認）
        $success = $this->publisher->publishToNative($this->testConfigPath, false);
        
        // 應該返回 true（覆蓋成功）
        $this->assertTrue($success);
        
        // 檔案內容應該已更新
        $newContent = file_get_contents($this->testConfigPath);
        $this->assertNotEquals($originalContent, $newContent);
        
        // 驗證新內容是正確的配置
        $config = require $this->testConfigPath;
        $this->assertIsArray($config);
        $this->assertArrayHasKey('baseUrl', $config);
    }
    
    /**
     * 測試建立配置目錄
     */
    public function testCreateConfigDirectory(): void
    {
        $testDir = $this->testDir . '/nested/config/directory';
        
        $success = $this->publisher->createConfigDirectory($testDir);
        
        $this->assertTrue($success);
        $this->assertDirectoryExists($testDir);
        $this->assertTrue(is_writable($testDir));
    }
    
    /**
     * 測試建立已存在的目錄
     */
    public function testCreateExistingDirectory(): void
    {
        $testDir = $this->testDir . '/existing';
        mkdir($testDir, 0755, true);
        
        $success = $this->publisher->createConfigDirectory($testDir);
        
        $this->assertTrue($success);
        $this->assertDirectoryExists($testDir);
    }
    
    /**
     * 測試建立不可寫的目錄
     */
    public function testCreateDirectoryWithInvalidPath(): void
    {
        // 使用空字串作為無效路徑
        $success = $this->publisher->createConfigDirectory('');
        
        $this->assertFalse($success);
    }
    
    /**
     * 測試發布狀態檢查
     */
    public function testGetPublishStatus(): void
    {
        // 測試檔案不存在的狀態
        $status = $this->publisher->getPublishStatus($this->testConfigPath);
        
        $this->assertIsArray($status);
        $this->assertArrayHasKey('published', $status);
        $this->assertArrayHasKey('target_path', $status);
        $this->assertArrayHasKey('environment', $status);
        $this->assertFalse($status['published']);
        $this->assertEquals($this->testConfigPath, $status['target_path']);
        
        // 發布檔案後再次檢查
        $this->publisher->publishToNative($this->testConfigPath, false);
        $status = $this->publisher->getPublishStatus($this->testConfigPath);
        
        $this->assertTrue($status['published']);
        $this->assertArrayHasKey('file_size', $status);
        $this->assertArrayHasKey('modified_time', $status);
        $this->assertArrayHasKey('readable', $status);
        $this->assertArrayHasKey('writable', $status);
        $this->assertTrue($status['readable']);
        $this->assertTrue($status['writable']);
        $this->assertGreaterThan(0, $status['file_size']);
    }
    
    /**
     * 測試發布到無效路徑
     */
    public function testPublishToInvalidPath(): void
    {
        $this->expectException(ConfigurationException::class);
        
        // 使用一個確定會失敗的路徑
        // 在 Windows 上，使用一個不存在的磁碟機
        // 在 Unix 上，使用一個沒有權限的路徑
        if (DIRECTORY_SEPARATOR === '\\') {
            // Windows: 使用不存在的磁碟機
            $invalidPath = 'Z:\\this\\path\\does\\not\\exist\\file-service.php';
        } else {
            // Unix: 使用 /dev/null 作為目錄（這會失敗）
            $invalidPath = '/dev/null/invalid/config/file-service.php';
        }
        
        $this->publisher->publishConfigFile($invalidPath, false);
    }
    
    /**
     * 測試複製配置檔案的基本功能
     */
    public function testPublishConfigFile(): void
    {
        $success = $this->publisher->publishConfigFile($this->testConfigPath, false, '測試環境');
        
        $this->assertTrue($success);
        $this->assertFileExists($this->testConfigPath);
        
        // 檢查檔案權限（在 Windows 上可能不同）
        $permissions = fileperms($this->testConfigPath) & 0777;
        // 在 Windows 上，檔案權限可能是 0666 或其他值
        $this->assertTrue($permissions > 0, '檔案應該有讀寫權限');
    }
    
    /**
     * 測試 Laravel 環境發布（模擬）
     */
    public function testPublishToLaravelWithoutLaravelEnvironment(): void
    {
        $this->expectException(ConfigurationException::class);
        $this->expectExceptionMessage('config_path 函數不存在');
        
        // 在非 Laravel 環境中嘗試使用 Laravel 發布方式
        $this->publisher->publishToLaravel();
    }
    
    /**
     * 測試獲取預設發布狀態（自動偵測環境）
     */
    public function testGetPublishStatusWithAutoDetection(): void
    {
        $status = $this->publisher->getPublishStatus();
        
        $this->assertIsArray($status);
        $this->assertArrayHasKey('published', $status);
        $this->assertArrayHasKey('target_path', $status);
        $this->assertArrayHasKey('environment', $status);
        
        // 在測試環境中應該偵測為原生 PHP 環境
        $this->assertEquals('原生 PHP', $status['environment']);
    }
    
    /**
     * 遞迴刪除目錄
     * 
     * @param string $dir 目錄路徑
     */
    private function removeDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }
        
        $files = array_diff(scandir($dir), ['.', '..']);
        
        foreach ($files as $file) {
            $path = $dir . DIRECTORY_SEPARATOR . $file;
            if (is_dir($path)) {
                $this->removeDirectory($path);
            } else {
                unlink($path);
            }
        }
        
        rmdir($dir);
    }
}