<?php

declare(strict_types=1);

namespace FDMC\FileServiceClient\Tests;

use FDMC\FileServiceClient\GlobalConfigManager;
use PHPUnit\Framework\TestCase;

/**
 * 配置合併整合測試
 * 測試完整的配置合併流程，包括優先級處理和環境變數覆蓋
 */
class ConfigMergeIntegrationTest extends TestCase
{
    protected function setUp(): void
    {
        // 載入 helpers.php 以確保 env() 函數可用
        require_once __DIR__ . '/../src/Core/helpers.php';

        // 清除運行時配置
        GlobalConfigManager::clearRuntimeConfig();
        GlobalConfigManager::resetEnvironmentDetection();

        // 清除環境變數
        $this->clearTestEnvironmentVariables();
    }

    protected function tearDown(): void
    {
        // 清理測試環境
        GlobalConfigManager::clearRuntimeConfig();
        GlobalConfigManager::resetEnvironmentDetection();
        $this->clearTestEnvironmentVariables();
    }

    /**
     * 測試配置優先級：運行時配置 > 環境變數 > 專案配置 > 預設配置
     */
    public function testConfigurationPriority(): void
    {
        // 設定環境變數
        $_ENV['FILE_SERVICE_BASE_URL'] = 'https://env.example.com';
        $_ENV['FILE_SERVICE_TIMEOUT'] = '45';

        // 獲取基礎配置（應該包含環境變數覆蓋）
        $config = GlobalConfigManager::config();

        // 驗證環境變數覆蓋生效
        $this->assertEquals('https://env.example.com', $config['baseUrl']);
        $this->assertEquals(45, $config['http']['timeout']);

        // 設定運行時配置（應該覆蓋環境變數）
        GlobalConfigManager::set('baseUrl', 'https://runtime.example.com');
        GlobalConfigManager::set('http.timeout', 60);

        $config = GlobalConfigManager::config();

        // 驗證運行時配置優先級最高
        $this->assertEquals('https://runtime.example.com', $config['baseUrl']);
        $this->assertEquals(60, $config['http']['timeout']);
    }

    /**
     * 測試巢狀配置合併
     */
    public function testNestedConfigMerging(): void
    {
        // 設定環境變數覆蓋部分 HTTP 配置
        $_ENV['FILE_SERVICE_SSL_VERIFY'] = 'true';
        $_ENV['FILE_SERVICE_CONNECT_TIMEOUT'] = '15';

        $config = GlobalConfigManager::config();

        // 驗證巢狀配置正確合併
        $this->assertTrue($config['http']['verify']);
        $this->assertEquals(15, $config['http']['connect_timeout']);

        // 驗證未覆蓋的配置保持預設值
        $this->assertEquals(30, $config['http']['timeout']); // 預設值
        $this->assertEquals(60, $config['http']['read_timeout']); // 預設值

        // 動態修改巢狀配置
        GlobalConfigManager::set('http.retry_attempts', 5);

        $config = GlobalConfigManager::config();
        $this->assertEquals(5, $config['http']['retry_attempts']);

        // 驗證其他配置未受影響
        $this->assertTrue($config['http']['verify']);
        $this->assertEquals(15, $config['http']['connect_timeout']);
    }

    /**
     * 測試陣列配置合併
     */
    public function testArrayConfigMerging(): void
    {
        // 動態添加 headers
        GlobalConfigManager::set('headers.Authorization', 'Bearer token123');
        GlobalConfigManager::set('headers.X-Custom-Header', 'custom-value');

        $config = GlobalConfigManager::config();

        // 驗證陣列合併正確
        $this->assertEquals('application/json', $config['headers']['Content-Type']); // 預設值保留
        $this->assertEquals('application/json', $config['headers']['Accept']); // 預設值保留
        $this->assertEquals('Bearer token123', $config['headers']['Authorization']); // 新增值
        $this->assertEquals('custom-value', $config['headers']['X-Custom-Header']); // 新增值
    }

    /**
     * 測試環境變數型別轉換
     */
    public function testEnvironmentVariableTypeConversion(): void
    {
        // 設定各種型別的環境變數
        $_ENV['FILE_SERVICE_TIMEOUT'] = '120';
        $_ENV['FILE_SERVICE_SSL_VERIFY'] = 'true';
        $_ENV['FILE_SERVICE_CACHE_ENABLED'] = 'false';
        $_ENV['FILE_SERVICE_MAX_FILE_SIZE'] = '209715200'; // 200MB

        $config = GlobalConfigManager::config();

        // 驗證型別轉換正確
        $this->assertIsInt($config['http']['timeout']);
        $this->assertEquals(120, $config['http']['timeout']);

        $this->assertIsBool($config['http']['verify']);
        $this->assertTrue($config['http']['verify']);

        $this->assertIsBool($config['cache']['enabled']);
        $this->assertFalse($config['cache']['enabled']);

        $this->assertIsInt($config['upload']['max_file_size']);
        $this->assertEquals(209715200, $config['upload']['max_file_size']);
    }

    /**
     * 測試批量配置設定
     */
    public function testBatchConfigurationSetting(): void
    {
        $batchConfig = [
            'baseUrl' => 'https://batch.example.com',
            'http.timeout' => 90,
            'http.verify' => true,
            'upload.max_file_size' => 52428800, // 50MB
        ];

        GlobalConfigManager::set($batchConfig);

        $config = GlobalConfigManager::config();

        $this->assertEquals('https://batch.example.com', $config['baseUrl']);
        $this->assertEquals(90, $config['http']['timeout']);
        $this->assertTrue($config['http']['verify']);
        $this->assertEquals(52428800, $config['upload']['max_file_size']);
    }

    /**
     * 測試配置移除功能
     */
    public function testConfigurationRemoval(): void
    {
        // 設定一些運行時配置
        GlobalConfigManager::set('custom_key', 'custom_value');
        GlobalConfigManager::set('http.custom_timeout', 999);

        $config = GlobalConfigManager::config();
        $this->assertEquals('custom_value', $config['custom_key']);
        $this->assertEquals(999, $config['http']['custom_timeout']);

        // 移除配置
        GlobalConfigManager::forget('custom_key');
        GlobalConfigManager::forget('http.custom_timeout');

        $config = GlobalConfigManager::config();
        $this->assertArrayNotHasKey('custom_key', $config);
        $this->assertArrayNotHasKey('custom_timeout', $config['http']);

        // 驗證其他配置未受影響
        $this->assertArrayHasKey('baseUrl', $config);
        $this->assertArrayHasKey('timeout', $config['http']);
    }

    /**
     * 測試配置重置
     */
    public function testConfigurationReset(): void
    {
        // 設定一些運行時配置
        GlobalConfigManager::set('baseUrl', 'https://runtime.example.com');
        GlobalConfigManager::set('http.timeout', 999);

        $config = GlobalConfigManager::config();
        $this->assertEquals('https://runtime.example.com', $config['baseUrl']);
        $this->assertEquals(999, $config['http']['timeout']);

        // 清除運行時配置
        GlobalConfigManager::clearRuntimeConfig();

        $config = GlobalConfigManager::config();

        // 驗證回到預設值（或環境變數值）
        $this->assertNotEquals('https://runtime.example.com', $config['baseUrl']);
        $this->assertNotEquals(999, $config['http']['timeout']);
    }

    /**
     * 測試複雜的配置合併場景
     */
    public function testComplexConfigMergeScenario(): void
    {
        // 1. 設定環境變數
        $_ENV['FILE_SERVICE_BASE_URL'] = 'https://env.example.com';
        $_ENV['FILE_SERVICE_TIMEOUT'] = '45';
        $_ENV['FILE_SERVICE_SSL_VERIFY'] = 'true';

        // 2. 設定運行時配置
        GlobalConfigManager::set([
            'baseUrl' => 'https://runtime.example.com',
            'http.connect_timeout' => 20,
            'headers.Authorization' => 'Bearer runtime-token',
            'custom' => [
                'feature_flag' => true,
                'debug_mode' => false,
            ],
        ]);

        $config = GlobalConfigManager::config();

        // 驗證最終合併結果
        $this->assertEquals('https://runtime.example.com', $config['baseUrl']); // 運行時覆蓋環境變數
        $this->assertEquals(45, $config['http']['timeout']); // 環境變數覆蓋預設值
        $this->assertTrue($config['http']['verify']); // 環境變數覆蓋預設值
        $this->assertEquals(20, $config['http']['connect_timeout']); // 運行時設定
        $this->assertEquals(60, $config['http']['read_timeout']); // 保持預設值

        // 驗證陣列合併
        $this->assertEquals('application/json', $config['headers']['Content-Type']); // 預設值保留
        $this->assertEquals('Bearer runtime-token', $config['headers']['Authorization']); // 運行時添加

        // 驗證自訂配置
        $this->assertTrue($config['custom']['feature_flag']);
        $this->assertFalse($config['custom']['debug_mode']);
    }

    /**
     * 測試無效環境變數處理
     */
    public function testInvalidEnvironmentVariableHandling(): void
    {
        // 設定無效的環境變數
        $_ENV['FILE_SERVICE_TIMEOUT'] = 'invalid_number';
        $_ENV['FILE_SERVICE_SSL_VERIFY'] = 'invalid_boolean';

        $config = GlobalConfigManager::config();

        // 驗證無效值被忽略，使用預設值
        $this->assertEquals(30, $config['http']['timeout']); // 預設值
        $this->assertFalse($config['http']['verify']); // 預設值
    }

    /**
     * 清除測試環境變數
     */
    private function clearTestEnvironmentVariables(): void
    {
        $envKeys = [
            'FILE_SERVICE_BASE_URL',
            'FILE_SERVICE_TIMEOUT',
            'FILE_SERVICE_CONNECT_TIMEOUT',
            'FILE_SERVICE_READ_TIMEOUT',
            'FILE_SERVICE_SSL_VERIFY',
            'FILE_SERVICE_UPLOAD_TIMEOUT',
            'FILE_SERVICE_MAX_FILE_SIZE',
            'FILE_SERVICE_DOWNLOAD_TIMEOUT',
            'FILE_SERVICE_CACHE_ENABLED',
            'FILE_SERVICE_CACHE_DRIVER',
            'FILE_SERVICE_CACHE_TTL',
            'FILE_SERVICE_LOG_ENABLED',
            'FILE_SERVICE_LOG_LEVEL',
            'FILE_SERVICE_LOG_CHANNEL',
            'FILE_SERVICE_RETRY_MAX_ATTEMPTS',
            'FILE_SERVICE_RETRY_DELAY',
            'FILE_SERVICE_RETRY_MULTIPLIER',
        ];

        foreach ($envKeys as $key) {
            unset($_ENV[$key]);
            unset($_SERVER[$key]);
        }
    }
}