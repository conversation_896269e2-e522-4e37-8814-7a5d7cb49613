<?php

declare(strict_types=1);

namespace FDMC\FileServiceClient\Commands;

use Illuminate\Console\Command;
use FDMC\FileServiceClient\ConfigPublisher;
use FDMC\FileServiceClient\ConfigurationException;

/**
 * Laravel Artisan 配置發布命令
 * 
 * 提供 Laravel 環境中的配置發布功能
 */
class LaravelPublishCommand extends Command
{
    /**
     * 命令名稱和簽名
     */
    protected $signature = 'file-service:publish-config 
                            {--force : 強制覆蓋現有配置檔案}
                            {--target= : 指定目標配置檔案路徑}';

    /**
     * 命令描述
     */
    protected $description = '發布 File Service Client 配置檔案';

    /**
     * 配置發布器
     */
    private ConfigPublisher $publisher;

    /**
     * 建構函數
     */
    public function __construct(ConfigPublisher $publisher)
    {
        parent::__construct();
        $this->publisher = $publisher;
    }

    /**
     * 執行命令
     */
    public function handle(): int
    {
        try {
            $this->info('File Service Client - 配置發布');
            $this->line('=====================================');

            // 獲取選項
            $force = $this->option('force');
            $target = $this->option('target');

            // 檢查發布狀態
            $status = $this->publisher->getPublishStatus($target);
            $this->displayStatus($status);

            // 如果檔案已存在且未強制覆蓋，詢問使用者
            if ($status['published'] && !$force) {
                if (!$this->confirm("配置檔案已存在於 {$status['target_path']}，是否要覆蓋？", false)) {
                    $this->info('取消發布操作。');
                    return 0;
                }
                $force = true;
            }

            // 執行發布
            $this->info('正在發布配置檔案...');
            $success = $this->publisher->publishToLaravel($target, $force);

            if ($success) {
                $this->info('✓ 配置檔案發布成功！');
                $this->displayNextSteps($target ?? config_path('file-service.php'));
                return 0;
            } else {
                $this->error('✗ 配置檔案發布失敗。');
                return 1;
            }

        } catch (ConfigurationException $e) {
            $this->error('✗ 發布失敗：' . $e->getMessage());
            $this->displayRepairSuggestions($e->getRepairSuggestions());
            return 1;
        } catch (\Exception $e) {
            $this->error('✗ 未預期的錯誤：' . $e->getMessage());
            return 1;
        }
    }

    /**
     * 顯示發布狀態
     * 
     * @param array $status 狀態資訊
     */
    private function displayStatus(array $status): void
    {
        $this->line('');
        $this->line('<comment>發布狀態：</comment>');
        $this->line("環境類型：{$status['environment']}");
        $this->line("目標路徑：{$status['target_path']}");

        if ($status['published']) {
            $this->line('<info>狀態：✓ 配置檔案已存在</info>');
            $this->line("檔案大小：" . $this->formatFileSize($status['file_size']));
            $this->line("修改時間：" . date('Y-m-d H:i:s', $status['modified_time']));
            $this->line("可讀：" . ($status['readable'] ? '是' : '否'));
            $this->line("可寫：" . ($status['writable'] ? '是' : '否'));
        } else {
            $this->line('<comment>狀態：- 配置檔案不存在</comment>');
        }

        $this->line('');
    }

    /**
     * 顯示下一步操作
     * 
     * @param string $configPath 配置檔案路徑
     */
    private function displayNextSteps(string $configPath): void
    {
        $this->line('');
        $this->line('<comment>下一步操作：</comment>');
        $this->line("1. 編輯配置檔案：{$configPath}");
        $this->line('2. 根據您的需求調整配置項目');
        $this->line('3. 設定環境變數（可選）：');
        $this->line('   FILE_SERVICE_BASE_URL=https://your-api.example.com');
        $this->line('4. 在您的 Laravel 應用中使用：');
        $this->line("   \$config = config('file-service.baseUrl');");
        $this->line('   或使用全域函數：file_service_config(\'baseUrl\')');
    }

    /**
     * 顯示修復建議
     * 
     * @param array $suggestions 修復建議
     */
    private function displayRepairSuggestions(array $suggestions): void
    {
        if (empty($suggestions)) {
            return;
        }

        $this->line('');
        $this->line('<comment>修復建議：</comment>');
        foreach ($suggestions as $suggestion) {
            $this->line("• " . $suggestion);
        }
    }

    /**
     * 格式化檔案大小
     * 
     * @param int $bytes 位元組數
     * @return string 格式化後的大小
     */
    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $factor = floor((strlen((string) $bytes) - 1) / 3);

        return sprintf("%.1f %s", $bytes / pow(1024, $factor), $units[$factor]);
    }
}