<?php

namespace FDMC\FileServiceClient\Tests;

use PHPUnit\Framework\TestCase;
use FDMC\FileServiceClient\FileServiceClient;
use FDMC\FileServiceClient\StorageManager;
use FDMC\FileServiceClient\ConfigManager;

class ConfigIntegrationTest extends TestCase
{
    protected function setUp(): void
    {
        // 模擬 env 函數
        $this->mockEnvFunction();

        // 清除配置快取
        ConfigManager::clearCache();
    }

    /**
     * 模擬 Laravel 的 env() 函數
     */
    private function mockEnvFunction(): void
    {
        if (!function_exists('env')) {
            eval ('
                function env($key, $default = null) {
                    $value = $_ENV[$key] ?? getenv($key);
                    if ($value === false) {
                        return $default;
                    }
                    return $value;
                }
            ');
        }
    }

    protected function tearDown(): void
    {
        // 清除配置快取
        ConfigManager::clearCache();
    }

    public function testFileServiceClientWithDefaultConfig()
    {
        $client = new FileServiceClient();

        // 檢查是否能正確初始化
        $this->assertInstanceOf(FileServiceClient::class, $client);

        // 檢查基礎 URL 是否正確設置
        $baseUrl = $client->getBaseUrl();
        $this->assertIsString($baseUrl);
        $this->assertNotEmpty($baseUrl);
    }

    public function testFileServiceClientWithCustomOptions()
    {
        $customOptions = [
            'baseUrl' => 'https://test.api.com',
            'headers' => [
                'Authorization' => 'Bearer test-token',
            ],
        ];

        $client = new FileServiceClient($customOptions);

        // 檢查自定義 baseUrl 是否生效
        $this->assertEquals('https://test.api.com', $client->getBaseUrl());
    }

    public function testStorageManagerWithDefaultConfig()
    {
        $storage = new StorageManager();

        // 檢查是否能正確初始化
        $this->assertInstanceOf(StorageManager::class, $storage);
    }

    public function testStorageManagerWithCustomOptions()
    {
        $customOptions = [
            'baseUrl' => 'https://storage.test.com',
            'http' => [
                'timeout' => 60,
            ],
        ];

        $storage = new StorageManager($customOptions);

        // 檢查是否能正確初始化
        $this->assertInstanceOf(StorageManager::class, $storage);
    }

    public function testConfigMerging()
    {
        // 測試配置合併邏輯
        $userOptions = [
            'baseUrl' => 'https://custom.api.com',
            'http' => [
                'timeout' => 45,
                // connect_timeout 不設置，應該使用預設值
            ],
            'headers' => [
                'Authorization' => 'Bearer token',
                // Content-Type 不設置，應該使用預設值
            ],
        ];

        $client = new FileServiceClient($userOptions);

        // 檢查自定義值是否生效
        $this->assertEquals('https://custom.api.com', $client->getBaseUrl());

        // 檢查 HTTP 客戶端是否正確初始化
        $httpClient = $client->getHttpClient();
        $this->assertNotNull($httpClient);
    }

    public function testEnvironmentVariableOverride()
    {
        // 設置環境變數
        putenv('FILE_SERVICE_BASE_URL=https://env.test.com');

        // 清除配置快取以重新載入
        ConfigManager::clearCache();

        $client = new FileServiceClient();

        // 檢查環境變數是否生效
        $this->assertEquals('https://env.test.com', $client->getBaseUrl());

        // 清理環境變數
        putenv('FILE_SERVICE_BASE_URL');
    }

    public function testUserOptionsOverrideEnvironmentVariables()
    {
        // 設置環境變數
        putenv('FILE_SERVICE_BASE_URL=https://env.test.com');

        // 清除配置快取
        ConfigManager::clearCache();

        // 使用者選項應該覆蓋環境變數
        $client = new FileServiceClient([
            'baseUrl' => 'https://user.test.com',
        ]);

        // 檢查使用者選項是否優先
        $this->assertEquals('https://user.test.com', $client->getBaseUrl());

        // 清理環境變數
        putenv('FILE_SERVICE_BASE_URL');
    }

    public function testBackwardCompatibility()
    {
        // 測試舊的使用方式是否仍然有效
        $oldStyleOptions = [
            'baseUrl' => 'https://old.api.com',
            'timeout' => 30,
            'connect_timeout' => 10,
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'Authorization' => 'Bearer old-token',
            ],
            'verify' => false,
        ];

        $client = new FileServiceClient($oldStyleOptions);

        // 檢查是否能正確初始化
        $this->assertInstanceOf(FileServiceClient::class, $client);
        $this->assertEquals('https://old.api.com', $client->getBaseUrl());
    }
}