<?php

declare(strict_types=1);

namespace FDMC\FileServiceClient;

/**
 * 配置日誌記錄器
 * 
 * 提供配置系統的日誌記錄功能，支援不同的日誌級別和輸出方式。
 */
class ConfigLogger
{
    // 日誌級別常數
    public const LEVEL_DEBUG = 100;
    public const LEVEL_INFO = 200;
    public const LEVEL_WARNING = 300;
    public const LEVEL_ERROR = 400;
    public const LEVEL_CRITICAL = 500;

    /**
     * 日誌級別名稱對應
     */
    private const LEVEL_NAMES = [
        self::LEVEL_DEBUG => 'DEBUG',
        self::LEVEL_INFO => 'INFO',
        self::LEVEL_WARNING => 'WARNING',
        self::LEVEL_ERROR => 'ERROR',
        self::LEVEL_CRITICAL => 'CRITICAL',
    ];

    /**
     * 最小日誌級別
     */
    private int $minLevel;

    /**
     * 日誌處理器列表
     */
    private array $handlers = [];

    /**
     * 單例實例
     */
    private static ?self $instance = null;

    /**
     * 建構子
     *
     * @param int $minLevel 最小日誌級別
     */
    public function __construct(int $minLevel = self::LEVEL_INFO)
    {
        $this->minLevel = $minLevel;
        $this->addDefaultHandlers();
    }

    /**
     * 獲取單例實例
     *
     * @return self
     */
    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 設定最小日誌級別
     *
     * @param int $level 日誌級別
     * @return self
     */
    public function setMinLevel(int $level): self
    {
        $this->minLevel = $level;
        return $this;
    }

    /**
     * 添加日誌處理器
     *
     * @param callable $handler 日誌處理器
     * @return self
     */
    public function addHandler(callable $handler): self
    {
        $this->handlers[] = $handler;
        return $this;
    }

    /**
     * 清除所有處理器
     *
     * @return self
     */
    public function clearHandlers(): self
    {
        $this->handlers = [];
        return $this;
    }

    /**
     * 記錄除錯訊息
     *
     * @param string $message 訊息
     * @param array $context 上下文資料
     * @return void
     */
    public function debug(string $message, array $context = []): void
    {
        $this->log(self::LEVEL_DEBUG, $message, $context);
    }

    /**
     * 記錄資訊訊息
     *
     * @param string $message 訊息
     * @param array $context 上下文資料
     * @return void
     */
    public function info(string $message, array $context = []): void
    {
        $this->log(self::LEVEL_INFO, $message, $context);
    }

    /**
     * 記錄警告訊息
     *
     * @param string $message 訊息
     * @param array $context 上下文資料
     * @return void
     */
    public function warning(string $message, array $context = []): void
    {
        $this->log(self::LEVEL_WARNING, $message, $context);
    }

    /**
     * 記錄錯誤訊息
     *
     * @param string $message 訊息
     * @param array $context 上下文資料
     * @return void
     */
    public function error(string $message, array $context = []): void
    {
        $this->log(self::LEVEL_ERROR, $message, $context);
    }

    /**
     * 記錄嚴重錯誤訊息
     *
     * @param string $message 訊息
     * @param array $context 上下文資料
     * @return void
     */
    public function critical(string $message, array $context = []): void
    {
        $this->log(self::LEVEL_CRITICAL, $message, $context);
    }

    /**
     * 記錄例外
     *
     * @param \Throwable $exception 例外
     * @param int $level 日誌級別
     * @param array $context 額外上下文資料
     * @return void
     */
    public function logException(\Throwable $exception, int $level = self::LEVEL_ERROR, array $context = []): void
    {
        $context = array_merge($context, [
            'exception' => $exception,
            'exception_class' => get_class($exception),
            'exception_code' => $exception->getCode(),
            'exception_file' => $exception->getFile(),
            'exception_line' => $exception->getLine(),
            'stack_trace' => $exception->getTraceAsString(),
        ]);

        $this->log($level, $exception->getMessage(), $context);
    }

    /**
     * 記錄配置載入事件
     *
     * @param string $source 配置來源
     * @param array $context 上下文資料
     * @return void
     */
    public function logConfigLoad(string $source, array $context = []): void
    {
        $this->info("配置已載入", array_merge($context, ['source' => $source]));
    }

    /**
     * 記錄配置驗證事件
     *
     * @param bool $success 驗證是否成功
     * @param array $context 上下文資料
     * @return void
     */
    public function logConfigValidation(bool $success, array $context = []): void
    {
        if ($success) {
            $this->info("配置驗證成功", $context);
        } else {
            $this->warning("配置驗證失敗", $context);
        }
    }

    /**
     * 記錄型別轉換事件
     *
     * @param string $key 配置鍵
     * @param mixed $originalValue 原始值
     * @param mixed $convertedValue 轉換後的值
     * @param string $type 目標型別
     * @return void
     */
    public function logTypeConversion(string $key, mixed $originalValue, mixed $convertedValue, string $type): void
    {
        $this->debug("型別轉換成功", [
            'key' => $key,
            'original_value' => $originalValue,
            'converted_value' => $convertedValue,
            'target_type' => $type,
        ]);
    }

    /**
     * 記錄型別轉換失敗事件
     *
     * @param string $key 配置鍵
     * @param mixed $value 原始值
     * @param string $type 目標型別
     * @param string $reason 失敗原因
     * @return void
     */
    public function logTypeConversionFailure(string $key, mixed $value, string $type, string $reason): void
    {
        $this->warning("型別轉換失敗", [
            'key' => $key,
            'value' => $value,
            'target_type' => $type,
            'reason' => $reason,
        ]);
    }

    /**
     * 記錄日誌
     *
     * @param int $level 日誌級別
     * @param string $message 訊息
     * @param array $context 上下文資料
     * @return void
     */
    public function log(int $level, string $message, array $context = []): void
    {
        if ($level < $this->minLevel) {
            return;
        }

        $record = [
            'level' => $level,
            'level_name' => self::LEVEL_NAMES[$level] ?? 'UNKNOWN',
            'message' => $message,
            'context' => $context,
            'datetime' => new \DateTimeImmutable(),
            'channel' => 'file-service-config',
        ];

        foreach ($this->handlers as $handler) {
            $handler($record);
        }
    }

    /**
     * 添加預設處理器
     *
     * @return void
     */
    private function addDefaultHandlers(): void
    {
        // 錯誤日誌處理器
        $this->addHandler(function (array $record): void {
            if ($record['level'] >= self::LEVEL_ERROR) {
                error_log($this->formatRecord($record));
            }
        });

        // 開發模式處理器（如果啟用）
        if ($this->isDevelopmentMode()) {
            $this->addHandler(function (array $record): void {
                if ($record['level'] >= self::LEVEL_WARNING) {
                    echo $this->formatRecord($record) . "\n";
                }
            });
        }
    }

    /**
     * 格式化日誌記錄
     *
     * @param array $record 日誌記錄
     * @return string 格式化後的字串
     */
    private function formatRecord(array $record): string
    {
        $datetime = $record['datetime']->format('Y-m-d H:i:s');
        $level = $record['level_name'];
        $channel = $record['channel'];
        $message = $record['message'];
        
        $formatted = "[{$datetime}] {$channel}.{$level}: {$message}";
        
        if (!empty($record['context'])) {
            $contextStr = $this->formatContext($record['context']);
            $formatted .= " {$contextStr}";
        }
        
        return $formatted;
    }

    /**
     * 格式化上下文資料
     *
     * @param array $context 上下文資料
     * @return string 格式化後的字串
     */
    private function formatContext(array $context): string
    {
        $parts = [];
        
        foreach ($context as $key => $value) {
            if ($key === 'exception' && $value instanceof \Throwable) {
                continue; // 例外資訊已在其他欄位中處理
            }
            
            if (is_scalar($value) || is_null($value)) {
                $parts[] = "{$key}=" . var_export($value, true);
            } else {
                $parts[] = "{$key}=" . json_encode($value, JSON_UNESCAPED_UNICODE);
            }
        }
        
        return '{' . implode(', ', $parts) . '}';
    }

    /**
     * 檢查是否為開發模式
     *
     * @return bool
     */
    private function isDevelopmentMode(): bool
    {
        // 檢查常見的開發環境指標
        $env = $_ENV['APP_ENV'] ?? $_SERVER['APP_ENV'] ?? 'production';
        return in_array(strtolower($env), ['development', 'dev', 'local', 'testing']);
    }

    /**
     * 獲取日誌級別名稱
     *
     * @param int $level 日誌級別
     * @return string 級別名稱
     */
    public static function getLevelName(int $level): string
    {
        return self::LEVEL_NAMES[$level] ?? 'UNKNOWN';
    }

    /**
     * 從名稱獲取日誌級別
     *
     * @param string $name 級別名稱
     * @return int 日誌級別
     */
    public static function getLevelFromName(string $name): int
    {
        $name = strtoupper($name);
        $levels = array_flip(self::LEVEL_NAMES);
        return $levels[$name] ?? self::LEVEL_INFO;
    }
}