<?php

namespace FDMC\FileServiceClient\Http;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\RequestException;
use Psr\Http\Message\ResponseInterface;
use FDMC\FileServiceClient\FileServiceException;

/**
 * Guzzle HTTP 客戶端實現
 * 基於 Guzzle HTTP 庫的具體實現，作為內部組件使用
 */
class GuzzleHttpClient implements HttpClientInterface
{
    private Client $httpClient;
    private string $baseUrl;
    private array $defaultHeaders;

    /**
     * 初始化 HTTP 客戶端
     * @param array $options 選項配置（已經與配置檔案合併）
     * @throws FileServiceException 初始化失敗時拋出例外
     */
    public function __construct(array $options = [])
    {
        // 防呆機制 1: 驗證選項參數
        if (!is_array($options)) {
            throw new FileServiceException('選項參數必須是陣列');
        }

        // 防呆機制 2: 取得並驗證基礎 URL
        // 選項已經通過 ConfigManager 合併了配置檔案和環境變數
        $baseUrl = $options['baseUrl'] ?? 'http://localhost:8080';

        // 防呆機制 3: 驗證基礎 URL 格式
        if (!empty($baseUrl)) {
            $baseUrl = trim($baseUrl);

            // 檢查 URL 格式是否有效
            if (!filter_var($baseUrl, FILTER_VALIDATE_URL)) {
                throw new FileServiceException('無效的基礎 URL 格式: ' . $baseUrl);
            }

            // 檢查是否為 HTTP/HTTPS 協議
            $parsedUrl = parse_url($baseUrl);
            if (!isset($parsedUrl['scheme']) || !in_array($parsedUrl['scheme'], ['http', 'https'], true)) {
                throw new FileServiceException('基礎 URL 必須使用 HTTP 或 HTTPS 協議: ' . $baseUrl);
            }
        }

        $this->baseUrl = rtrim($baseUrl, '/');

        // 防呆機制 4: 設置預設標頭（從配置檔案獲取）
        $this->defaultHeaders = $options['headers'] ?? [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];

        // 防呆機制 5: 驗證並設置客戶端選項
        try {
            // 防呆機制 6: 驗證超時設定
            if (isset($options['timeout'])) {
                if (!is_numeric($options['timeout']) || $options['timeout'] <= 0) {
                    throw new FileServiceException('超時設定必須是正數');
                }
            }

            if (isset($options['connect_timeout'])) {
                if (!is_numeric($options['connect_timeout']) || $options['connect_timeout'] <= 0) {
                    throw new FileServiceException('連接超時設定必須是正數');
                }
            }

            // 防呆機制 8: 建構 Guzzle 客戶端選項
            $clientOptions = [
                'base_uri' => "{$this->baseUrl}/api",
                'timeout' => $options['timeout'] ?? 30,
                'connect_timeout' => $options['connect_timeout'] ?? 10,
                'read_timeout' => $options['read_timeout'] ?? 60,
                'headers' => $this->defaultHeaders,
                'verify' => $options['verify'] ?? false,
                'http_errors' => $options['http_errors'] ?? false,
            ];

            // 防呆機制 9: 初始化 Guzzle 客戶端
            $this->httpClient = new Client($clientOptions);

        } catch (\Exception $e) {
            // 防呆機制 10: 捕獲初始化過程中的所有異常
            throw new FileServiceException(
                'HTTP 客戶端初始化失敗: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }

    /**
     * 發送 JSON API 請求
     * @param string $method 請求方法
     * @param string $endpoint 端點路徑
     * @param array $options 選項配置
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function sendJsonRequest(string $method, string $endpoint, array $options = []): array
    {
        // 使用輔助方法進行基本驗證
        $method = $this->validateMethod($method);
        $endpoint = $this->validateEndpoint($endpoint);
        $this->validateOptions($options);
        $this->validateBasicConfiguration();

        try {
            // 設置合理的預設值
            $defaultOptions = [
                'timeout' => 30,
                'connect_timeout' => 10,
                'read_timeout' => 60,
                'headers' => $this->defaultHeaders,
            ];

            // 合併選項，用戶選項優先
            $mergedOptions = array_merge_recursive($defaultOptions, $options);

            // 驗證合併後的選項
            $this->validateMergedOptions($mergedOptions);

            $response = $this->httpClient->request($method, $endpoint, $mergedOptions);
            return $this->parseJsonResponse($response);

        } catch (GuzzleException $e) {
            $this->handleGuzzleException($e, $method, $endpoint, 'HTTP JSON');
        } catch (\Exception $e) {
            $this->handleGeneralException($e, $method, $endpoint, 'JSON');
        }
    }

    /**
     * 發送 Multipart 表單請求（用於檔案上傳）
     * @param string $endpoint 端點路徑
     * @param array $options 選項配置，必須包含 'multipart' 陣列
     * @return array 解析後的資料
     * @throws FileServiceException 請求失敗時拋出例外
     * 
     * 使用範例：
     * $options = [
     *     'multipart' => [
     *         [
     *             'name'     => 'object_id',
     *             'contents' => 'some-object-id'
     *         ],
     *         [
     *             'name'     => 'file',
     *             'contents' => fopen('/path/to/file.jpg', 'r'),
     *             'filename' => 'file.jpg'
     *         ]
     *     ],
     *     'query' => ['user_id' => 123]
     * ];
     */
    public function sendMultipartRequest(string $endpoint, array $options = []): array
    {
        // 使用輔助方法進行基本驗證
        $method = $this->validateMethod('POST'); // multipart 請求固定使用 POST
        $endpoint = $this->validateEndpoint($endpoint);
        $this->validateOptions($options);
        $this->validateBasicConfiguration();

        // 驗證 multipart 數據是否存在
        if (!isset($options['multipart']) || !is_array($options['multipart'])) {
            throw new FileServiceException('multipart 請求必須包含 multipart 陣列數據');
        }

        try {
            // 設置合理的預設值
            $defaultOptions = [
                'timeout' => 120, // multipart 上傳可能需要更長時間
                'connect_timeout' => 10,
                'read_timeout' => 120,
                'headers' => [
                    'Accept' => 'application/json', // 只保留 Accept 標頭，讓 Guzzle 自動設置 Content-Type
                ],
            ];

            // 合併選項，用戶選項優先
            $mergedOptions = array_merge_recursive($defaultOptions, $options);

            // 驗證合併後的選項
            $this->validateMergedOptions($mergedOptions);

            // 驗證 multipart 數據格式
            foreach ($mergedOptions['multipart'] as $index => $part) {
                if (!is_array($part)) {
                    throw new FileServiceException("multipart 數據索引 {$index} 必須是陣列");
                }

                if (!isset($part['name'])) {
                    throw new FileServiceException("multipart 數據索引 {$index} 必須包含 'name' 欄位");
                }

                if (!isset($part['contents']) && !isset($part['filename'])) {
                    throw new FileServiceException("multipart 數據索引 {$index} 必須包含 'contents' 或 'filename' 欄位");
                }
            }

            $response = $this->httpClient->request($method, $endpoint, $mergedOptions);

            return $this->parseJsonResponse($response);

        } catch (GuzzleException $e) {
            $this->handleGuzzleException($e, $method, $endpoint, 'HTTP multipart');
        } catch (\Exception $e) {
            $this->handleGeneralException($e, $method, $endpoint, 'multipart');
        }
    }

    /**
     * 發送原始 HTTP 請求（用於下載檔案等）
     * @param string $method 請求方法
     * @param string $endpoint 端點路徑
     * @param array $options 選項配置
     * @return ResponseInterface 原始回應
     * @throws FileServiceException 請求失敗時拋出例外
     */
    public function sendRawRequest(string $method, string $endpoint, array $options = []): ResponseInterface
    {
        // 使用輔助方法進行基本驗證
        $method = $this->validateMethod($method);
        $endpoint = $this->validateEndpoint($endpoint);
        $this->validateOptions($options);
        $this->validateBasicConfiguration();

        try {
            // 設置合理的預設值（原始請求不強制 JSON 標頭）
            $defaultOptions = [
                'timeout' => 30,
                'connect_timeout' => 10,
                'read_timeout' => 60,
            ];

            // 合併選項，用戶選項優先
            $mergedOptions = array_merge_recursive($defaultOptions, $options);

            // 驗證合併後的選項
            $this->validateMergedOptions($mergedOptions);

            return $this->httpClient->request($method, $endpoint, $mergedOptions);

        } catch (GuzzleException $e) {
            $this->handleGuzzleException($e, $method, $endpoint, 'HTTP 原始');
        } catch (\Exception $e) {
            $this->handleGeneralException($e, $method, $endpoint, '原始');
        }
    }

    /**
     * 驗證 HTTP 請求方法
     * @param string $method 請求方法
     * @return string 正規化後的請求方法
     * @throws FileServiceException 無效的請求方法時拋出例外
     */
    private function validateMethod(string $method): string
    {
        $method = strtoupper(trim($method));
        $allowedMethods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS'];
        if (empty($method) || !in_array($method, $allowedMethods, true)) {
            throw new FileServiceException('無效的 HTTP 請求方法: ' . $method);
        }
        return $method;
    }

    /**
     * 驗證並正規化端點路徑
     * @param string $endpoint 端點路徑
     * @return string 正規化後的端點路徑
     * @throws FileServiceException 無效的端點路徑時拋出例外
     */
    private function validateEndpoint(string $endpoint): string
    {
        $endpoint = trim($endpoint);
        if (empty($endpoint)) {
            throw new FileServiceException('端點路徑不能為空');
        }

        // 確保端點以斜線開頭
        if (!str_starts_with($endpoint, '/')) {
            $endpoint = '/' . $endpoint;
        }

        return $endpoint;
    }

    /**
     * 驗證選項參數
     * @param mixed $options 選項參數
     * @throws FileServiceException 無效的選項參數時拋出例外
     */
    private function validateOptions($options): void
    {
        if (!is_array($options)) {
            throw new FileServiceException('選項參數必須是陣列');
        }
    }

    /**
     * 檢查基礎設定是否正確
     * @throws FileServiceException 基礎設定錯誤時拋出例外
     */
    private function validateBasicConfiguration(): void
    {
        // 檢查基礎 URL 是否已設置
        if (empty($this->baseUrl)) {
            throw new FileServiceException('基礎 URL 尚未設置，無法發送請求');
        }

        // 檢查 HTTP 客戶端是否已初始化
        if (!isset($this->httpClient)) {
            throw new FileServiceException('HTTP 客戶端尚未初始化');
        }
    }

    /**
     * 驗證合併後的請求選項
     * @param array $mergedOptions 合併後的選項
     * @throws FileServiceException 選項驗證失敗時拋出例外
     */
    private function validateMergedOptions(array $mergedOptions): void
    {
        // 確保標頭格式正確
        if (isset($mergedOptions['headers']) && !is_array($mergedOptions['headers'])) {
            throw new FileServiceException('標頭必須是陣列格式');
        }

        // 驗證超時設定
        if (isset($mergedOptions['timeout']) && (!is_numeric($mergedOptions['timeout']) || $mergedOptions['timeout'] <= 0)) {
            throw new FileServiceException('超時設定必須是正數');
        }
    }

    /**
     * 處理 Guzzle 例外並轉換為自定義例外
     * @param GuzzleException $e Guzzle 例外
     * @param string $method 請求方法
     * @param string $endpoint 端點路徑
     * @param string $requestType 請求類型（用於錯誤訊息）
     * @throws FileServiceException 轉換後的例外
     */
    private function handleGuzzleException(GuzzleException $e, string $method, string $endpoint, string $requestType = 'HTTP'): void
    {
        $errorMessage = sprintf(
            '%s 請求失敗 [%s %s]: %s',
            $requestType,
            $method,
            $endpoint,
            $e->getMessage()
        );

        // 如果有回應，嘗試取得更多錯誤資訊
        if ($e instanceof RequestException && $e->hasResponse()) {
            $response = $e->getResponse();
            $statusCode = $response->getStatusCode();
            $reasonPhrase = $response->getReasonPhrase();
            $errorMessage .= sprintf(' (HTTP %d: %s)', $statusCode, $reasonPhrase);

            // 嘗試解析錯誤回應內容
            try {
                $body = $response->getBody()->getContents();
                $errorData = json_decode($body, true);
                if (json_last_error() === JSON_ERROR_NONE && isset($errorData['message'])) {
                    $errorMessage .= ' - ' . $errorData['message'];
                }
            } catch (\Exception $parseException) {
                // 忽略解析錯誤，使用原始錯誤訊息
            }
        }

        throw new FileServiceException($errorMessage, $e->getCode(), $e);
    }

    /**
     * 處理一般例外並轉換為自定義例外
     * @param \Exception $e 一般例外
     * @param string $method 請求方法
     * @param string $endpoint 端點路徑
     * @param string $requestType 請求類型（用於錯誤訊息）
     * @throws FileServiceException 轉換後的例外
     */
    private function handleGeneralException(\Exception $e, string $method, string $endpoint, string $requestType = 'HTTP'): void
    {
        throw new FileServiceException(
            sprintf('發送%s請求時發生未預期的錯誤 [%s %s]: %s', $requestType, $method, $endpoint, $e->getMessage()),
            0,
            $e
        );
    }

    /**
     * 解析 JSON 回應
     * @param ResponseInterface $response 回應
     * @return array 解析後的資料
     * @throws FileServiceException 解析失敗時拋出例外
     */
    private function parseJsonResponse(ResponseInterface $response): array
    {
        // 防呆機制 1: 檢查回應物件
        if (!$response instanceof ResponseInterface) {
            throw new FileServiceException('無效的回應物件');
        }

        // 防呆機制 2: 檢查 HTTP 狀態碼
        $statusCode = $response->getStatusCode();

        /**
         * 400 以上，500 以下，表示用戶端錯誤，需要回傳錯誤訊息
         */
        if ($statusCode >= 400 && $statusCode < 500) {
            $body = $response->getBody()->getContents();
            $errorData = json_decode($body, true);
            if (json_last_error() === JSON_ERROR_NONE && isset($errorData['message'])) {
                throw new FileServiceException($errorData['message'], $statusCode);
            }
            throw new FileServiceException(
                sprintf('HTTP 請求失敗，狀態碼: %d %s', $statusCode, $response->getBody()->getContents())
            );
        }

        /**
         * 200 以上，300 以下，表示成功
         */
        if ($statusCode < 200 || $statusCode >= 300) {
            throw new FileServiceException(
                sprintf('HTTP 請求失敗，狀態碼: %d %s', $statusCode, $response->getBody()->getContents())
            );
        }

        // 防呆機制 3: 檢查 Content-Type
        $contentType = $response->getHeaderLine('Content-Type');
        if (!empty($contentType) && !str_contains(strtolower($contentType), 'application/json')) {
            throw new FileServiceException(
                sprintf('回應內容類型不是 JSON: %s', $contentType)
            );
        }

        // 防呆機制 4: 安全取得回應內容
        try {
            $body = $response->getBody()->getContents();
        } catch (\Exception $e) {
            throw new FileServiceException('無法讀取回應內容: ' . $e->getMessage());
        }

        // 防呆機制 5: 檢查回應內容是否為空
        if (empty($body)) {
            throw new FileServiceException('回應內容為空');
        }

        // 防呆機制 6: 檢查 JSON 格式並解析
        $data = json_decode($body, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new FileServiceException(
                sprintf(
                    '無法解析 JSON 回應: %s (原始內容: %s)',
                    json_last_error_msg(),
                    substr($body, 0, 200) . (strlen($body) > 200 ? '...' : '')
                )
            );
        }

        // 防呆機制 7: 檢查解析結果
        if (!is_array($data)) {
            throw new FileServiceException('JSON 回應格式錯誤，預期為陣列格式');
        }

        // 防呆機制 8: 檢查 API 回應結構
        if (!array_key_exists('success', $data)) {
            throw new FileServiceException('API 回應缺少 success 欄位');
        }

        // 防呆機制 9: 檢查 API 是否成功
        if (!$data['success']) {
            $errorMessage = $data['message'] ?? $data['error'] ?? '未知錯誤';
            $errorCode = $data['code'] ?? 0;

            // 提供更詳細的錯誤資訊
            if (isset($data['errors']) && is_array($data['errors'])) {
                $errorMessage .= ' (詳細錯誤: ' . implode(', ', $data['errors']) . ')';
            }

            throw new FileServiceException($errorMessage, $errorCode);
        }

        return $data;
    }

    /**
     * 設置自定義 HTTP 標頭
     * @param array $headers 自定義 HTTP 標頭
     * @throws FileServiceException 設置失敗時拋出例外
     */
    public function setHeaders(array $headers): void
    {
        // 防呆機制 1: 驗證標頭參數
        if (!is_array($headers)) {
            throw new FileServiceException('標頭參數必須是陣列');
        }

        // 防呆機制 2: 檢查是否為空陣列
        if (empty($headers)) {
            throw new FileServiceException('標頭參數不能為空陣列');
        }

        // 防呆機制 3: 驗證標頭格式
        foreach ($headers as $name => $value) {
            if (!is_string($name) || empty(trim($name))) {
                throw new FileServiceException('標頭名稱必須是非空字串');
            }

            if (!is_string($value) && !is_numeric($value)) {
                throw new FileServiceException('標頭值必須是字串或數值');
            }
        }

        // 防呆機制 4: 檢查 HTTP 客戶端是否已初始化
        if (!isset($this->httpClient)) {
            throw new FileServiceException('HTTP 客戶端尚未初始化，無法設置標頭');
        }

        try {
            // 防呆機制 5: 安全合併標頭
            $this->defaultHeaders = array_merge($this->defaultHeaders, $headers);

            // 更新 HTTP 客戶端的預設標頭
            $config = $this->httpClient->getConfig();
            $config['headers'] = array_merge($config['headers'] ?? [], $headers);

        } catch (\Exception $e) {
            // 防呆機制 6: 捕獲設置過程中的異常
            throw new FileServiceException(
                '設置 HTTP 標頭失敗: ' . $e->getMessage(),
                0,
                $e
            );
        }
    }

    /**
     * 獲取當前設置的基礎 URL
     * @return string 基礎 URL
     */
    public function getBaseUrl(): string
    {
        return $this->baseUrl;
    }

    /**
     * 獲取預設標頭
     * @return array 預設標頭
     */
    public function getDefaultHeaders(): array
    {
        return $this->defaultHeaders;
    }

    /**
     * 獲取底層的 Guzzle HTTP 客戶端（用於高級使用案例）
     * @return Client Guzzle HTTP 客戶端
     */
    public function getGuzzleClient(): Client
    {
        return $this->httpClient;
    }
}