<?php

declare(strict_types=1);

namespace FDMC\FileServiceClient\Tests;

use FDMC\FileServiceClient\GlobalConfigManager;
use PHPUnit\Framework\TestCase;
use ReflectionClass;

/**
 * 服務提供者核心功能測試
 * 
 * 測試不依賴 Laravel 框架的核心邏輯
 */
class ServiceProviderCoreTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        $this->mockEnvFunction();
    }

    /**
     * 模擬 Laravel 的 env() 函數
     */
    private function mockEnvFunction(): void
    {
        if (!function_exists('env')) {
            eval ('
                function env($key, $default = null) {
                    $value = $_ENV[$key] ?? getenv($key);
                    if ($value === false) {
                        return $default;
                    }
                    return $value;
                }
            ');
        }
    }
    /**
     * 測試配置檔案是否存在
     */
    public function testConfigFileExists(): void
    {
        $configPath = __DIR__ . '/../config/file-service.php';
        $this->assertFileExists($configPath);

        // 測試配置檔案是否可以載入
        $config = require $configPath;
        $this->assertIsArray($config);

        // 測試必要的配置項目是否存在
        $this->assertArrayHasKey('baseUrl', $config);
        $this->assertArrayHasKey('http', $config);
        $this->assertArrayHasKey('upload', $config);
        $this->assertArrayHasKey('download', $config);
    }

    /**
     * 測試配置檔案結構
     */
    public function testConfigFileStructure(): void
    {
        $config = require __DIR__ . '/../config/file-service.php';

        // 測試 HTTP 配置結構
        $this->assertArrayHasKey('timeout', $config['http']);
        $this->assertArrayHasKey('connect_timeout', $config['http']);
        $this->assertArrayHasKey('read_timeout', $config['http']);
        $this->assertArrayHasKey('verify', $config['http']);

        // 測試上傳配置結構
        $this->assertArrayHasKey('timeout', $config['upload']);
        $this->assertArrayHasKey('max_file_size', $config['upload']);
        $this->assertArrayHasKey('allowed_mime_types', $config['upload']);

        // 測試下載配置結構
        $this->assertArrayHasKey('timeout', $config['download']);
        $this->assertArrayHasKey('stream_buffer_size', $config['download']);
        $this->assertArrayHasKey('large_file_threshold', $config['download']);
    }

    /**
     * 測試環境變數處理
     */
    public function testEnvironmentVariableProcessing(): void
    {
        // 設定測試環境變數
        $_ENV['FILE_SERVICE_BASE_URL'] = 'https://test.example.com';
        $_ENV['FILE_SERVICE_TIMEOUT'] = '45';
        $_ENV['FILE_SERVICE_SSL_VERIFY'] = 'true';

        $config = require __DIR__ . '/../config/file-service.php';

        // 驗證環境變數是否正確處理
        $this->assertEquals('https://test.example.com', $config['baseUrl']);
        $this->assertEquals(45, $config['http']['timeout']);
        $this->assertTrue($config['http']['verify']);

        // 清理環境變數
        unset($_ENV['FILE_SERVICE_BASE_URL']);
        unset($_ENV['FILE_SERVICE_TIMEOUT']);
        unset($_ENV['FILE_SERVICE_SSL_VERIFY']);
    }

    /**
     * 測試預設值
     */
    public function testDefaultValues(): void
    {
        // 確保沒有相關環境變數
        unset($_ENV['FILE_SERVICE_BASE_URL']);
        unset($_ENV['FILE_SERVICE_TIMEOUT']);

        $config = require __DIR__ . '/../config/file-service.php';

        // 驗證預設值
        $this->assertEquals('http://localhost:8080', $config['baseUrl']);
        $this->assertEquals(30, $config['http']['timeout']);
        $this->assertEquals(10, $config['http']['connect_timeout']);
        $this->assertFalse($config['http']['verify']);
    }

    /**
     * 測試全域函數是否已載入
     */
    public function testGlobalFunctionsLoaded(): void
    {
        // 載入全域函數
        require_once __DIR__ . '/../src/Core/helpers.php';

        // 驗證主要函數是否存在
        $this->assertTrue(function_exists('file_service_config'));
        $this->assertTrue(function_exists('file_service_config_set'));
        $this->assertTrue(function_exists('file_service_config_forget'));
        $this->assertTrue(function_exists('file_service_config_all'));
        $this->assertTrue(function_exists('file_service_config_has'));
    }

    /**
     * 測試全域函數基本功能
     */
    public function testGlobalFunctionBasicUsage(): void
    {
        // 載入全域函數
        require_once __DIR__ . '/../src/Core/helpers.php';

        // 測試獲取所有配置
        $allConfig = file_service_config();
        $this->assertIsArray($allConfig);
        $this->assertNotEmpty($allConfig);

        // 測試獲取特定配置
        $baseUrl = file_service_config('baseUrl');
        $this->assertIsString($baseUrl);

        // 測試預設值
        $nonExistent = file_service_config('non.existent.key', 'default_value');
        $this->assertEquals('default_value', $nonExistent);
    }

    /**
     * 測試配置設定功能
     */
    public function testConfigurationSetting(): void
    {
        // 載入全域函數
        require_once __DIR__ . '/../src/Core/helpers.php';

        // 測試設定單一配置
        file_service_config_set('test.key', 'test_value');
        $value = file_service_config('test.key');
        $this->assertEquals('test_value', $value);

        // 測試批量設定配置
        file_service_config_set([
            'test.batch1' => 'value1',
            'test.batch2' => 'value2'
        ]);

        $this->assertEquals('value1', file_service_config('test.batch1'));
        $this->assertEquals('value2', file_service_config('test.batch2'));
    }

    /**
     * 測試配置移除功能
     */
    public function testConfigurationForget(): void
    {
        // 載入全域函數
        require_once __DIR__ . '/../src/Core/helpers.php';

        // 設定一個測試配置
        file_service_config_set('test.forget', 'will_be_removed');
        $this->assertEquals('will_be_removed', file_service_config('test.forget'));

        // 移除配置
        file_service_config_forget('test.forget');

        // 驗證配置已被移除（應該返回預設值）
        $value = file_service_config('test.forget', 'default');
        $this->assertEquals('default', $value);
    }

    /**
     * 測試配置存在檢查
     */
    public function testConfigurationHas(): void
    {
        // 載入全域函數
        require_once __DIR__ . '/../src/Core/helpers.php';

        // 測試存在的配置
        $this->assertTrue(file_service_config_has('baseUrl'));
        $this->assertTrue(file_service_config_has('http.timeout'));

        // 測試不存在的配置
        $this->assertFalse(file_service_config_has('non.existent.key'));

        // 設定一個測試配置並檢查
        file_service_config_set('test.exists', 'value');
        $this->assertTrue(file_service_config_has('test.exists'));
    }

    /**
     * 測試 GlobalConfigManager 整合
     */
    public function testGlobalConfigManagerIntegration(): void
    {
        // 載入全域函數
        require_once __DIR__ . '/../src/Core/helpers.php';

        // 測試全域函數是否正確使用 GlobalConfigManager
        $directValue = GlobalConfigManager::config('baseUrl');
        $functionValue = file_service_config('baseUrl');

        $this->assertEquals($directValue, $functionValue);

        // 測試設定是否同步
        GlobalConfigManager::set('test.sync', 'sync_value');
        $this->assertEquals('sync_value', file_service_config('test.sync'));

        file_service_config_set('test.sync2', 'sync_value2');
        $this->assertEquals('sync_value2', GlobalConfigManager::config('test.sync2'));
    }
}