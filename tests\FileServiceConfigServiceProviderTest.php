<?php

declare(strict_types=1);

namespace FDMC\FileServiceClient\Tests;

use FDMC\FileServiceClient\FileServiceConfigServiceProvider;
use FDMC\FileServiceClient\GlobalConfigManager;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

/**
 * Laravel 服務提供者測試
 * 
 * 注意：這些測試使用模擬物件來測試核心功能，
 * 不依賴實際的 Laravel 框架
 */
class FileServiceConfigServiceProviderTest extends TestCase
{
    private FileServiceConfigServiceProvider $provider;
    private MockLaravelApp $app;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // 建立模擬的 Laravel 容器
        $this->app = new MockLaravelApp();
        
        // 建立服務提供者實例
        $this->provider = new FileServiceConfigServiceProvider($this->app);
        
        // 模擬 Laravel 的 config_path 函數
        if (!function_exists('config_path')) {
            function config_path($path = '') {
                return '/app/config/' . $path;
            }
        }
    }
    
    protected function tearDown(): void
    {
        // 清理全域狀態
        parent::tearDown();
    }
    
    /**
     * 測試服務註冊功能
     */
    public function testRegisterServices(): void
    {
        // 執行註冊
        $this->provider->register();
        
        // 驗證服務是否已註冊
        $this->assertTrue($this->app->bound(GlobalConfigManager::class));
        $this->assertTrue($this->app->bound('file-service.config'));
        
        // 驗證服務是否為單例
        $instance1 = $this->app->make(GlobalConfigManager::class);
        $instance2 = $this->app->make(GlobalConfigManager::class);
        $this->assertSame($instance1, $instance2);
        
        // 驗證別名是否正確
        $aliasInstance = $this->app->make('file-service.config');
        $this->assertSame($instance1, $aliasInstance);
    }
    
    /**
     * 測試配置發布功能
     */
    public function testPublishesConfiguration(): void
    {
        // 這個測試主要驗證 boot 方法不會拋出例外
        $this->expectNotToPerformAssertions();
        
        // 執行啟動
        $this->provider->boot();
    }
    
    /**
     * 測試全域函數衝突檢測邏輯
     */
    public function testCanRegisterGlobalFunctionLogic(): void
    {
        // 使用反射來測試私有方法
        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('canRegisterGlobalFunction');
        $method->setAccessible(true);
        
        // 測試方法是否返回布林值
        $result = $method->invoke($this->provider);
        $this->assertIsBool($result);
    }
    
    /**
     * 測試全域函數註冊條件檢查
     */
    public function testShouldRegisterGlobalFunctionConditions(): void
    {
        // 使用反射來測試私有方法
        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('shouldRegisterGlobalFunction');
        $method->setAccessible(true);
        
        // 測試方法是否返回布林值
        $result = $method->invoke($this->provider);
        $this->assertIsBool($result);
    }
    
    /**
     * 測試全域函數註冊功能
     */
    public function testRegisterGlobalConfigFunction(): void
    {
        // 模擬日誌服務
        $logger = $this->createMock(LoggerInterface::class);
        $this->app->instance('log', $logger);
        
        // 模擬配置啟用全域函數
        $this->app->instance('config', function ($key, $default = null) {
            if ($key === 'file-service.enable_global_config_function') {
                return true;
            }
            return $default;
        });
        
        // 使用反射來測試私有方法
        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('registerGlobalConfigFunction');
        $method->setAccessible(true);
        
        // 執行方法
        $method->invoke($this->provider);
        
        // 驗證 file_service_config 函數是否已載入
        $this->assertTrue(function_exists('file_service_config'));
    }
    
    /**
     * 測試日誌記錄功能
     */
    public function testLoggingMethods(): void
    {
        // 模擬日誌服務
        $logger = $this->createMock(LoggerInterface::class);
        $this->app->instance('log', $logger);
        
        // 測試警告日誌
        $logger->expects($this->once())
            ->method('warning')
            ->with('[FileService] Test warning message');
        
        // 使用反射來測試私有方法
        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('logFunctionConflict');
        $method->setAccessible(true);
        
        $method->invoke($this->provider, 'Test warning message');
    }
    
    /**
     * 測試服務提供者提供的服務列表
     */
    public function testProvidesServices(): void
    {
        $provides = $this->provider->provides();
        
        $this->assertIsArray($provides);
        $this->assertContains(GlobalConfigManager::class, $provides);
        $this->assertContains('file-service.config', $provides);
    }
    
    /**
     * 測試配置檔案路徑獲取
     */
    public function testGetDefaultConfigPath(): void
    {
        // 使用反射來測試私有方法
        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('getDefaultConfigPath');
        $method->setAccessible(true);
        
        $path = $method->invoke($this->provider);
        
        $this->assertIsString($path);
        $this->assertStringEndsWith('config/file-service.php', $path);
        $this->assertFileExists($path);
    }
    
    /**
     * 測試應該註冊全域函數的條件檢查
     */
    public function testShouldRegisterGlobalFunction(): void
    {
        // 模擬配置服務
        $this->app->instance('config', function ($key, $default = null) {
            if ($key === 'file-service.enable_global_config_function') {
                return false; // 預設不啟用
            }
            return $default;
        });
        
        // 使用反射來測試私有方法
        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('shouldRegisterGlobalFunction');
        $method->setAccessible(true);
        
        // 配置未啟用時應該返回 false
        $result = $method->invoke($this->provider);
        $this->assertFalse($result);
    }
    
    /**
     * 測試沒有日誌服務時的行為
     */
    public function testLoggingWithoutLogService(): void
    {
        // 確保沒有日誌服務
        $this->app->forgetInstance('log');
        
        // 使用反射來測試私有方法
        $reflection = new \ReflectionClass($this->provider);
        $method = $reflection->getMethod('logInfo');
        $method->setAccessible(true);
        
        // 應該不會拋出例外
        $this->expectNotToPerformAssertions();
        $method->invoke($this->provider, 'Test message');
    }
}
/**
 *
 模擬的 Laravel 應用程式容器
 */
class MockLaravelApp
{
    private array $bindings = [];
    private array $instances = [];
    private array $aliases = [];
    
    public function singleton(string $abstract, $concrete = null): void
    {
        $this->bindings[$abstract] = [
            'concrete' => $concrete,
            'singleton' => true,
        ];
    }
    
    public function bind(string $abstract, $concrete = null): void
    {
        $this->bindings[$abstract] = [
            'concrete' => $concrete,
            'singleton' => false,
        ];
    }
    
    public function instance(string $abstract, $instance): void
    {
        $this->instances[$abstract] = $instance;
    }
    
    public function alias(string $abstract, string $alias): void
    {
        $this->aliases[$alias] = $abstract;
    }
    
    public function bound(string $abstract): bool
    {
        return isset($this->bindings[$abstract]) || 
               isset($this->instances[$abstract]) ||
               isset($this->aliases[$abstract]);
    }
    
    public function make(string $abstract)
    {
        // 檢查實例
        if (isset($this->instances[$abstract])) {
            return $this->instances[$abstract];
        }
        
        // 檢查別名
        if (isset($this->aliases[$abstract])) {
            return $this->make($this->aliases[$abstract]);
        }
        
        // 檢查綁定
        if (isset($this->bindings[$abstract])) {
            $binding = $this->bindings[$abstract];
            $concrete = $binding['concrete'];
            
            if (is_callable($concrete)) {
                $instance = $concrete($this);
            } else {
                $instance = new $concrete();
            }
            
            if ($binding['singleton']) {
                $this->instances[$abstract] = $instance;
            }
            
            return $instance;
        }
        
        // 嘗試直接實例化
        return new $abstract();
    }
    
    public function forgetInstance(string $abstract): void
    {
        unset($this->instances[$abstract]);
    }
    
    public function offsetGet($key)
    {
        return $this->make($key);
    }
    
    public function offsetExists($key): bool
    {
        return $this->bound($key);
    }
}