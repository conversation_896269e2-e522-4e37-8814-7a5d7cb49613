<?php

/**
 * File Service Client 全域配置函數
 * 
 * 提供類似 Laravel 的全域配置存取介面
 */

use FDMC\FileServiceClient\GlobalConfigManager;
use FDMC\FileServiceClient\ConfigurationException;

if (!function_exists('env')) {
    /**
     * 獲取環境變數值（相容函數）
     * 提供與 Laravel env() 函數相容的功能
     * 
     * @param string $key 環境變數鍵
     * @param mixed $default 預設值
     * @return mixed 環境變數值
     */
    function env(string $key, $default = null)
    {
        // 優先從 $_ENV 獲取
        if (isset($_ENV[$key])) {
            return $_ENV[$key];
        }
        
        // 其次從 $_SERVER 獲取
        if (isset($_SERVER[$key])) {
            return $_SERVER[$key];
        }
        
        // 最後使用 getenv 函數
        $value = getenv($key);
        if ($value !== false) {
            return $value;
        }
        
        return $default;
    }
}

if (!function_exists('file_service_config')) {
    /**
     * 獲取或設定 File Service 配置值
     * 
     * 這是主要的配置存取介面，提供以下功能：
     * - 獲取配置值（支援點號語法）
     * - 動態設定配置值
     * - 批量設定配置
     * - 移除配置項目
     * 
     * @param string|array|null $key 配置鍵或配置陣列，null 時返回所有配置
     * @param mixed $default 預設值（僅在獲取配置時使用）
     * @return mixed 配置值或 void（設定時）
     * 
     * @throws ConfigurationException 當參數無效時
     * 
     * @example
     * // 獲取配置值
     * $baseUrl = file_service_config('baseUrl');
     * $timeout = file_service_config('http.timeout', 30);
     * 
     * // 獲取所有配置
     * $allConfig = file_service_config();
     * 
     * // 設定單一配置
     * file_service_config('baseUrl', 'https://api.example.com');
     * 
     * // 批量設定配置
     * file_service_config([
     *     'baseUrl' => 'https://api.example.com',
     *     'http.timeout' => 60
     * ]);
     */
    function file_service_config($key = null, $default = null)
    {
        // 參數驗證
        if ($key !== null && !is_string($key) && !is_array($key)) {
            throw ConfigurationException::invalidParameter(
                'key',
                'string, array or null',
                gettype($key)
            );
        }

        // 如果 $key 是陣列，表示要批量設定配置
        if (is_array($key)) {
            file_service_config_set($key);
            return;
        }

        // 獲取配置值（這是主要功能）
        return GlobalConfigManager::config($key, $default);
    }
}

if (!function_exists('file_service_config_set')) {
    /**
     * 設定 File Service 配置值
     * 
     * @param string|array $key 配置鍵或配置陣列
     * @param mixed $value 配置值（當 $key 為字串時使用）
     * @return void
     * 
     * @throws ConfigurationException 當參數無效時
     * 
     * @example
     * // 設定單一配置
     * file_service_config_set('baseUrl', 'https://api.example.com');
     * 
     * // 批量設定配置
     * file_service_config_set([
     *     'baseUrl' => 'https://api.example.com',
     *     'http.timeout' => 60
     * ]);
     */
    function file_service_config_set($key, $value = null): void
    {
        // 參數驗證
        if (!is_string($key) && !is_array($key)) {
            throw ConfigurationException::invalidParameter(
                'key',
                'string or array',
                gettype($key)
            );
        }

        if (is_array($key)) {
            // 批量設定配置
            file_service_config_validate_array($key);
            GlobalConfigManager::set($key);
        } else {
            // 單一配置設定
            if (func_num_args() < 2) {
                throw ConfigurationException::missingParameter('value');
            }
            
            file_service_config_validate_key($key);
            GlobalConfigManager::set($key, $value);
        }
    }
}

if (!function_exists('file_service_config_forget')) {
    /**
     * 移除 File Service 配置項目
     * 
     * @param string $key 配置鍵，支援點號語法
     * @return void
     * 
     * @throws ConfigurationException 當參數無效時
     * 
     * @example
     * // 移除單一配置
     * file_service_config_forget('cache.enabled');
     * 
     * // 移除巢狀配置
     * file_service_config_forget('http.timeout');
     */
    function file_service_config_forget(string $key): void
    {
        // 參數驗證
        file_service_config_validate_key($key);
        
        GlobalConfigManager::forget($key);
    }
}

if (!function_exists('file_service_config_all')) {
    /**
     * 獲取所有 File Service 配置
     * 
     * @return array 所有配置
     * 
     * @example
     * $allConfig = file_service_config_all();
     */
    function file_service_config_all(): array
    {
        return GlobalConfigManager::all();
    }
}

if (!function_exists('file_service_config_has')) {
    /**
     * 檢查 File Service 配置項目是否存在
     * 
     * @param string $key 配置鍵，支援點號語法
     * @return bool 配置項目是否存在
     * 
     * @throws ConfigurationException 當參數無效時
     * 
     * @example
     * if (file_service_config_has('cache.enabled')) {
     *     // 配置項目存在
     * }
     */
    function file_service_config_has(string $key): bool
    {
        // 參數驗證
        file_service_config_validate_key($key);
        
        $config = GlobalConfigManager::all();
        return file_service_config_array_has($config, $key);
    }
}

// 輔助函數（內部使用）

if (!function_exists('file_service_config_validate_key')) {
    /**
     * 驗證配置鍵格式
     * 
     * @param string $key 配置鍵
     * @return void
     * 
     * @throws ConfigurationException 當鍵格式無效時
     */
    function file_service_config_validate_key(string $key): void
    {
        if (empty($key)) {
            throw ConfigurationException::invalidParameter(
                'key',
                'non-empty string',
                'empty string'
            );
        }

        // 檢查鍵是否包含無效字符
        if (preg_match('/[^a-zA-Z0-9._-]/', $key)) {
            throw ConfigurationException::invalidConfigKey($key);
        }

        // 檢查點號語法是否正確
        if (strpos($key, '..') !== false || 
            strpos($key, '.') === 0 || 
            strrpos($key, '.') === strlen($key) - 1) {
            throw ConfigurationException::invalidConfigKey($key);
        }
    }
}

if (!function_exists('file_service_config_validate_array')) {
    /**
     * 驗證配置陣列格式
     * 
     * @param array $config 配置陣列
     * @return void
     * 
     * @throws ConfigurationException 當陣列格式無效時
     */
    function file_service_config_validate_array(array $config): void
    {
        if (empty($config)) {
            throw ConfigurationException::invalidParameter(
                'config',
                'non-empty array',
                'empty array'
            );
        }

        foreach ($config as $key => $value) {
            if (!is_string($key)) {
                throw ConfigurationException::invalidParameter(
                    'config key',
                    'string',
                    gettype($key)
                );
            }
            
            file_service_config_validate_key($key);
        }
    }
}



if (!function_exists('file_service_config_array_has')) {
    /**
     * 檢查陣列中是否存在指定的巢狀鍵
     * 
     * @param array $array 陣列
     * @param string $key 鍵，支援點號分隔
     * @return bool 鍵是否存在
     */
    function file_service_config_array_has(array $array, string $key): bool
    {
        if (strpos($key, '.') === false) {
            return array_key_exists($key, $array);
        }

        $keys = explode('.', $key);
        $current = $array;

        foreach ($keys as $nestedKey) {
            if (!is_array($current) || !array_key_exists($nestedKey, $current)) {
                return false;
            }
            $current = $current[$nestedKey];
        }

        return true;
    }
}