<?php

namespace FDMC\FileServiceClient\Tests;

use PHPUnit\Framework\TestCase;
use FDMC\FileServiceClient\Client\FileServiceClient;
use FDMC\FileServiceClient\Exceptions\FileServiceException;

class FileServiceClientTest extends TestCase
{
    private FileServiceClient $client;

    protected function setUp(): void
    {
        $this->client = new FileServiceClient(['baseUrl' => 'http://localhost:8000']);
    }

    public function testClientInitialization(): void
    {
        $this->assertInstanceOf(FileServiceClient::class, $this->client);
        $this->assertEquals('http://localhost:8000', $this->client->getBaseUrl());
    }

    public function testBuildObjectData(): void
    {
        $data = $this->client->buildObjectData(
            company_id: 1,
            scope: 'test',
            name: 'test.txt',
            type: 'file'
        );

        $this->assertIsArray($data);
        $this->assertEquals(1, $data['company_id']);
        $this->assertEquals('test', $data['scope']);
        $this->assertEquals('test.txt', $data['name']);
        $this->assertEquals('file', $data['type']);
    }

    public function testBuildObjectDataWithAllParameters(): void
    {
        $data = $this->client->buildObjectData(
            company_id: 1,
            scope: 'test',
            name: 'test.txt',
            type: 'file',
            parent: 'parent-id',
            storage_path: '/path/to/file',
            mimeType: 'text/plain',
            size: 1024,
            sha1: 'sha1hash',
            md5: 'md5hash'
        );

        $this->assertEquals('parent-id', $data['parent']);
        $this->assertEquals('/path/to/file', $data['storage_path']);
        $this->assertEquals('text/plain', $data['mimeType']);
        $this->assertEquals(1024, $data['size']);
        $this->assertEquals('sha1hash', $data['sha1']);
        $this->assertEquals('md5hash', $data['md5']);
    }



    public function testSetHeaders(): void
    {
        $this->client->setHeaders(['Authorization' => 'Bearer token123']);

        // 由於 defaultHeaders 是私有屬性，我們只能測試方法不會拋出錯誤
        $this->assertTrue(true);
    }

    public function testDownloadObjectWithInvalidObjectId(): void
    {
        $this->expectException(FileServiceException::class);
        $this->expectExceptionMessage("物件 ID 不能為空");

        $this->client->downloadObject('', 1);
    }

    public function testDownloadObjectWithInvalidUserId(): void
    {
        $this->expectException(FileServiceException::class);
        $this->expectExceptionMessage("使用者 ID 必須是正整數");

        $this->client->downloadObject('test-object-id', 0);
    }

    // 注意：determineStorageType 方法已被移除，相關測試已刪除







    public function testDownloadFromApLocalWithMissingPath(): void
    {
        $reflection = new \ReflectionClass($this->client);
        $method = $reflection->getMethod('downloadFromApLocal');
        $method->setAccessible(true);

        $this->expectException(FileServiceException::class);
        $this->expectExceptionMessage("AP_LOCAL 儲存類型缺少 storage_path 資訊");

        $objectData = [];
        $method->invokeArgs($this->client, [$objectData]);
    }

    public function testDownloadFromApLocalWithNonExistentFile(): void
    {
        $reflection = new \ReflectionClass($this->client);
        $method = $reflection->getMethod('downloadFromApLocal');
        $method->setAccessible(true);

        $this->expectException(FileServiceException::class);
        $this->expectExceptionMessage("本地檔案不存在:");

        $objectData = ['storage_path' => '/non/existent/file.txt'];
        $method->invokeArgs($this->client, [$objectData]);
    }

    public function testDownloadFromApLocalSuccess(): void
    {
        $reflection = new \ReflectionClass($this->client);
        $method = $reflection->getMethod('downloadFromApLocal');
        $method->setAccessible(true);

        // 建立一個測試檔案
        $testFile = tempnam(sys_get_temp_dir(), 'test_file');
        $testContent = 'This is test content for download';
        file_put_contents($testFile, $testContent);

        try {
            $objectData = ['storage_path' => $testFile];
            $stream = $method->invokeArgs($this->client, [$objectData]);

            $this->assertInstanceOf(\Psr\Http\Message\StreamInterface::class, $stream);
            $this->assertEquals($testContent, $stream->getContents());
        } finally {
            // 清理測試檔案
            if (file_exists($testFile)) {
                unlink($testFile);
            }
        }
    }
}