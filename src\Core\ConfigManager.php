<?php

namespace FDMC\FileServiceClient;

/**
 * 配置管理器（向後相容性包裝器）
 * 負責載入和管理 File Service Client 的配置
 * 
 * @deprecated 建議使用 GlobalConfigManager 或全域 file_service_config() 函數
 * 為了向後相容性保留此類別，內部委託給 GlobalConfigManager
 */
class ConfigManager
{
    private static ?array $config = null;
    private static ?string $configPath = null;
    
    /**
     * 是否啟用新的全域配置系統
     * 可以通過設定此屬性來控制是否使用新系統
     */
    private static bool $useGlobalConfig = true;

    /**
     * 載入配置檔案
     * @param string|null $configPath 配置檔案路徑，如果為 null 則使用預設路徑
     * @return array 配置陣列
     * @throws FileServiceException 配置檔案載入失敗時拋出例外
     */
    public static function loadConfig(?string $configPath = null): array
    {
        // 如果啟用新的全域配置系統，委託給 GlobalConfigManager
        if (self::$useGlobalConfig) {
            return self::loadConfigViaGlobalManager($configPath);
        }
        
        // 舊版載入邏輯（保持向後相容性）
        return self::loadConfigLegacy($configPath);
    }
    
    /**
     * 透過 GlobalConfigManager 載入配置（新方法）
     * 
     * @param string|null $configPath 配置檔案路徑
     * @return array 配置陣列
     */
    private static function loadConfigViaGlobalManager(?string $configPath = null): array
    {
        if ($configPath !== null) {
            // 如果指定了自訂路徑，暫時設定到 ConfigLoader
            // 這是為了向後相容性
            $loader = new ConfigLoader();
            $loader->setCustomPath($configPath);
            
            try {
                return $loader->loadFromFile($configPath);
            } catch (FileServiceException $e) {
                throw $e;
            }
        }
        
        // 使用全域配置管理器
        return GlobalConfigManager::config() ?? [];
    }
    
    /**
     * 舊版配置載入邏輯（保持向後相容性）
     * 
     * @param string|null $configPath 配置檔案路徑
     * @return array 配置陣列
     * @throws FileServiceException
     */
    private static function loadConfigLegacy(?string $configPath = null): array
    {
        // 如果已經載入過配置且路徑相同，直接返回快取的配置
        if (self::$config !== null && self::$configPath === $configPath) {
            return self::$config;
        }

        // 確定配置檔案路徑
        if ($configPath === null) {
            $configPath = self::getDefaultConfigPath();
        }

        // 驗證配置檔案路徑
        if (!is_string($configPath) || empty(trim($configPath))) {
            throw new FileServiceException('配置檔案路徑不能為空');
        }

        // 檢查配置檔案是否存在
        if (!file_exists($configPath)) {
            throw new FileServiceException("配置檔案不存在: {$configPath}");
        }

        // 檢查配置檔案是否可讀
        if (!is_readable($configPath)) {
            throw new FileServiceException("配置檔案無法讀取: {$configPath}");
        }

        try {
            // 載入配置檔案
            $config = require $configPath;

            // 驗證配置格式
            if (!is_array($config)) {
                throw new FileServiceException("配置檔案必須返回陣列格式: {$configPath}");
            }

            // 快取配置
            self::$config = $config;
            self::$configPath = $configPath;

            return $config;

        } catch (\Exception $e) {
            if ($e instanceof FileServiceException) {
                throw $e;
            }
            throw new FileServiceException("載入配置檔案失敗: {$e->getMessage()}", 0, $e);
        }
    }

    /**
     * 獲取配置值
     * @param string $key 配置鍵，支援點號分隔的巢狀鍵
     * @param mixed $default 預設值
     * @param string|null $configPath 配置檔案路徑
     * @return mixed 配置值
     */
    public static function get(string $key, $default = null, ?string $configPath = null)
    {
        // 如果使用新的全域配置系統且沒有指定自訂路徑
        if (self::$useGlobalConfig && $configPath === null) {
            return GlobalConfigManager::config($key, $default);
        }
        
        // 否則使用傳統方式載入配置
        $config = self::loadConfig($configPath);
        return self::getNestedValue($config, $key, $default);
    }

    /**
     * 獲取所有配置
     * @param string|null $configPath 配置檔案路徑
     * @return array 所有配置
     */
    public static function all(?string $configPath = null): array
    {
        // 如果使用新的全域配置系統且沒有指定自訂路徑
        if (self::$useGlobalConfig && $configPath === null) {
            return GlobalConfigManager::all();
        }
        
        return self::loadConfig($configPath);
    }

    /**
     * 合併使用者選項與預設配置
     * @param array $userOptions 使用者提供的選項
     * @param string|null $configPath 配置檔案路徑
     * @return array 合併後的配置
     */
    public static function mergeWithUserOptions(array $userOptions = [], ?string $configPath = null): array
    {
        // 如果使用新的全域配置系統且沒有指定自訂路徑
        if (self::$useGlobalConfig && $configPath === null) {
            $defaultConfig = GlobalConfigManager::all();
            
            // 如果使用者選項為空，直接返回預設配置
            if (empty($userOptions)) {
                return $defaultConfig;
            }
            
            // 使用 ConfigMerger 進行配置合併
            $merger = new ConfigMerger();
            return $merger->mergeUserOptions($defaultConfig, $userOptions);
        }
        
        // 傳統合併方式
        $defaultConfig = self::loadConfig($configPath);
        
        // 如果使用者選項為空，直接返回預設配置
        if (empty($userOptions)) {
            return $defaultConfig;
        }

        // 遞迴合併配置，使用者選項優先
        return self::arrayMergeRecursive($defaultConfig, $userOptions);
    }

    /**
     * 獲取 HTTP 客戶端配置
     * @param array $userOptions 使用者提供的選項
     * @param string|null $configPath 配置檔案路徑
     * @return array HTTP 客戶端配置
     */
    public static function getHttpClientConfig(array $userOptions = [], ?string $configPath = null): array
    {
        $config = self::mergeWithUserOptions($userOptions, $configPath);
        
        // 建構 HTTP 客戶端需要的配置格式
        $httpConfig = [
            'baseUrl' => $config['baseUrl'] ?? 'http://localhost:8080',
            'timeout' => $config['http']['timeout'] ?? 30,
            'connect_timeout' => $config['http']['connect_timeout'] ?? 10,
            'read_timeout' => $config['http']['read_timeout'] ?? 60,
            'verify' => $config['http']['verify'] ?? false,
            'http_errors' => $config['http']['http_errors'] ?? false,
            'headers' => $config['headers'] ?? [],
        ];

        return $httpConfig;
    }

    /**
     * 獲取上傳配置
     * @param array $userOptions 使用者提供的選項
     * @param string|null $configPath 配置檔案路徑
     * @return array 上傳配置
     */
    public static function getUploadConfig(array $userOptions = [], ?string $configPath = null): array
    {
        $config = self::mergeWithUserOptions($userOptions, $configPath);
        
        return $config['upload'] ?? [];
    }

    /**
     * 獲取下載配置
     * @param array $userOptions 使用者提供的選項
     * @param string|null $configPath 配置檔案路徑
     * @return array 下載配置
     */
    public static function getDownloadConfig(array $userOptions = [], ?string $configPath = null): array
    {
        $config = self::mergeWithUserOptions($userOptions, $configPath);
        
        return $config['download'] ?? [];
    }

    /**
     * 清除配置快取
     */
    public static function clearCache(): void
    {
        self::$config = null;
        self::$configPath = null;
        
        // 如果使用新的全域配置系統，也清除其運行時配置
        if (self::$useGlobalConfig) {
            GlobalConfigManager::clearRuntimeConfig();
        }
    }
    
    /**
     * 啟用新的全域配置系統
     * 
     * @return void
     */
    public static function enableGlobalConfig(): void
    {
        self::$useGlobalConfig = true;
    }
    
    /**
     * 禁用新的全域配置系統（使用舊版邏輯）
     * 
     * @return void
     */
    public static function disableGlobalConfig(): void
    {
        self::$useGlobalConfig = false;
    }
    
    /**
     * 檢查是否啟用了新的全域配置系統
     * 
     * @return bool
     */
    public static function isGlobalConfigEnabled(): bool
    {
        return self::$useGlobalConfig;
    }

    /**
     * 獲取預設配置檔案路徑
     * @return string 預設配置檔案路徑
     */
    private static function getDefaultConfigPath(): string
    {
        // 嘗試多個可能的配置檔案位置
        $possiblePaths = [
            // 專案根目錄的 config 資料夾
            __DIR__ . '/../config/file-service.php',
            
            // 當前目錄的 config 資料夾
            getcwd() . '/config/file-service.php',
            
            // Laravel 風格的配置路徑
            base_path('config/file-service.php'),
        ];

        foreach ($possiblePaths as $path) {
            if (file_exists($path)) {
                return $path;
            }
        }

        // 如果都找不到，返回預設路徑
        return __DIR__ . '/../config/file-service.php';
    }

    /**
     * 獲取巢狀陣列的值
     * @param array $array 陣列
     * @param string $key 鍵，支援點號分隔
     * @param mixed $default 預設值
     * @return mixed 值
     */
    private static function getNestedValue(array $array, string $key, $default = null)
    {
        if (strpos($key, '.') === false) {
            return $array[$key] ?? $default;
        }

        $keys = explode('.', $key);
        $value = $array;

        foreach ($keys as $nestedKey) {
            if (!is_array($value) || !array_key_exists($nestedKey, $value)) {
                return $default;
            }
            $value = $value[$nestedKey];
        }

        return $value;
    }

    /**
     * 遞迴合併陣列
     * @param array $array1 第一個陣列
     * @param array $array2 第二個陣列
     * @return array 合併後的陣列
     */
    private static function arrayMergeRecursive(array $array1, array $array2): array
    {
        $merged = $array1;

        foreach ($array2 as $key => $value) {
            if (is_array($value) && isset($merged[$key]) && is_array($merged[$key])) {
                $merged[$key] = self::arrayMergeRecursive($merged[$key], $value);
            } else {
                $merged[$key] = $value;
            }
        }

        return $merged;
    }

    /**
     * 檢查是否為 Laravel 環境
     * @return bool
     */
    private static function isLaravelEnvironment(): bool
    {
        return function_exists('base_path') && function_exists('config');
    }
}

/**
 * 輔助函數：獲取 base_path（如果不在 Laravel 環境中）
 * @param string $path 路徑
 * @return string 完整路徑
 */
if (!function_exists('base_path')) {
    function base_path(string $path = ''): string
    {
        return getcwd() . ($path ? DIRECTORY_SEPARATOR . ltrim($path, DIRECTORY_SEPARATOR) : '');
    }
}