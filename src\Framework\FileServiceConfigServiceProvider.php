<?php

declare(strict_types=1);

namespace FDMC\FileServiceClient;

use Illuminate\Support\ServiceProvider;
use ReflectionFunction;
use ReflectionException;
use FDMC\FileServiceClient\Commands\LaravelPublishCommand;

/**
 * Laravel 服務提供者，用於註冊 File Service 配置系統
 * 
 * 提供以下功能：
 * - 服務註冊和配置合併
 * - 配置檔案發布
 * - 可選的全域函數註冊
 * - 全域函數衝突檢測
 */
class FileServiceConfigServiceProvider extends ServiceProvider
{
    /**
     * 註冊服務到容器
     * 
     * 在此階段進行：
     * - 服務註冊
     * - 配置合併
     */
    public function register(): void
    {
        // 合併套件預設配置到 Laravel 配置系統
        $this->mergeConfigFrom(
            $this->getDefaultConfigPath(),
            'file-service'
        );
        
        // 註冊 GlobalConfigManager 為單例
        $this->app->singleton(GlobalConfigManager::class, function ($app) {
            return new GlobalConfigManager();
        });
        
        // 註冊 ConfigPublisher 為單例
        $this->app->singleton(ConfigPublisher::class, function ($app) {
            $logger = $app->bound(ConfigLogger::class) ? $app->make(ConfigLogger::class) : null;
            return new ConfigPublisher($logger);
        });
        
        // 註冊配置管理器別名
        $this->app->alias(GlobalConfigManager::class, 'file-service.config');
        $this->app->alias(ConfigPublisher::class, 'file-service.publisher');
    }
    
    /**
     * 啟動服務
     * 
     * 在此階段進行：
     * - 資源發布
     * - 全域函數註冊（可選）
     */
    public function boot(): void
    {
        // 發布配置檔案
        $this->publishes([
            $this->getDefaultConfigPath() => config_path('file-service.php'),
        ], 'file-service-config');
        
        // 可選的全域函數註冊
        if ($this->shouldRegisterGlobalFunction()) {
            $this->registerGlobalConfigFunction();
        }
        
        // 註冊 Artisan 命令
        if ($this->app->runningInConsole()) {
            $this->registerCommands();
        }
    }
    
    /**
     * 獲取套件預設配置檔案路徑
     */
    private function getDefaultConfigPath(): string
    {
        return __DIR__ . '/../config/file-service.php';
    }
    
    /**
     * 檢查是否應該註冊全域函數
     */
    private function shouldRegisterGlobalFunction(): bool
    {
        // 檢查配置是否啟用全域函數
        $enabled = config('file-service.enable_global_config_function', false);
        
        if (!$enabled) {
            return false;
        }
        
        // 檢查是否可以安全註冊全域函數
        return $this->canRegisterGlobalFunction();
    }
    
    /**
     * 檢查是否可以安全註冊全域函數
     * 
     * 檢測全域函數衝突，確保不會覆蓋現有函數
     */
    private function canRegisterGlobalFunction(): bool
    {
        // 如果 config 函數不存在，可以安全註冊
        if (!function_exists('config')) {
            return true;
        }
        
        try {
            $reflection = new ReflectionFunction('config');
            $fileName = $reflection->getFileName();
            
            // 如果是 Laravel 內建的 config 函數，則不註冊
            // Laravel 的 config 函數通常在 vendor/laravel 目錄下
            if ($fileName && str_contains($fileName, 'laravel')) {
                $this->logFunctionConflict('config 函數已由 Laravel 提供，跳過全域函數註冊');
                return false;
            }
            
            // 如果是其他來源的 config 函數，記錄警告但不註冊
            $this->logFunctionConflict("config 函數已存在於: {$fileName}");
            return false;
            
        } catch (ReflectionException $e) {
            // 無法獲取函數資訊，為安全起見不註冊
            $this->logFunctionConflict('無法檢測 config 函數來源，跳過註冊');
            return false;
        }
    }
    
    /**
     * 註冊全域配置函數
     */
    private function registerGlobalConfigFunction(): void
    {
        // 載入 helpers.php 檔案，其中包含全域函數定義
        $helpersPath = __DIR__ . '/helpers.php';
        
        if (file_exists($helpersPath)) {
            require_once $helpersPath;
            $this->logInfo('已成功註冊 file_service_config 全域函數');
        } else {
            $this->logWarning("helpers.php 檔案不存在: {$helpersPath}");
        }
    }
    
    /**
     * 記錄函數衝突資訊
     */
    private function logFunctionConflict(string $message): void
    {
        if ($this->app->bound('log')) {
            $this->app['log']->warning("[FileService] {$message}");
        }
    }
    
    /**
     * 記錄一般資訊
     */
    private function logInfo(string $message): void
    {
        if ($this->app->bound('log')) {
            $this->app['log']->info("[FileService] {$message}");
        }
    }
    
    /**
     * 記錄警告資訊
     */
    private function logWarning(string $message): void
    {
        if ($this->app->bound('log')) {
            $this->app['log']->warning("[FileService] {$message}");
        }
    }
    
    /**
     * 註冊 Artisan 命令
     */
    private function registerCommands(): void
    {
        $this->commands([
            LaravelPublishCommand::class,
        ]);
    }

    /**
     * 獲取配置發布器實例
     * 
     * @return ConfigPublisher 配置發布器實例
     */
    public function getConfigPublisher(): ConfigPublisher
    {
        return $this->app->make(ConfigPublisher::class);
    }
    
    /**
     * 檢查配置是否已發布
     * 
     * @return bool 配置是否已發布
     */
    public function isConfigPublished(): bool
    {
        $configPath = config_path('file-service.php');
        return file_exists($configPath);
    }
    
    /**
     * 獲取發布狀態
     * 
     * @return array 發布狀態資訊
     */
    public function getPublishStatus(): array
    {
        $publisher = $this->getConfigPublisher();
        return $publisher->getPublishStatus();
    }

    /**
     * 獲取服務提供者提供的服務
     */
    public function provides(): array
    {
        return [
            GlobalConfigManager::class,
            ConfigPublisher::class,
            'file-service.config',
            'file-service.publisher',
        ];
    }
}