<?php

declare(strict_types=1);

namespace FDMC\FileServiceClient\Tests;

use PHPUnit\Framework\TestCase;
use FDMC\FileServiceClient\GlobalConfigManager;
use FDMC\FileServiceClient\ConfigurationException;

/**
 * 全域配置函數測試
 */
class GlobalConfigFunctionTest extends TestCase
{
    protected function setUp(): void
    {
        // 模擬 env 函數
        $this->mockEnvFunction();

        // 載入全域函數
        require_once __DIR__ . '/../src/Core/helpers.php';

        // 清除運行時配置
        GlobalConfigManager::clearRuntimeConfig();
        GlobalConfigManager::resetEnvironmentDetection();
    }

    /**
     * 模擬 Laravel 的 env() 函數
     */
    private function mockEnvFunction(): void
    {
        if (!function_exists('env')) {
            eval ('
                function env($key, $default = null) {
                    $value = $_ENV[$key] ?? getenv($key);
                    if ($value === false) {
                        return $default;
                    }
                    return $value;
                }
            ');
        }
    }

    protected function tearDown(): void
    {
        // 清除運行時配置
        GlobalConfigManager::clearRuntimeConfig();
    }

    /**
     * 測試基本配置獲取功能
     */
    public function testBasicConfigGet(): void
    {
        // 測試獲取單一配置
        $baseUrl = file_service_config('baseUrl');
        $this->assertIsString($baseUrl);

        // 測試獲取巢狀配置
        $timeout = file_service_config('http.timeout');
        $this->assertIsInt($timeout);

        // 測試預設值
        $nonExistent = file_service_config('non.existent.key', 'default');
        $this->assertEquals('default', $nonExistent);
    }

    /**
     * 測試獲取所有配置
     */
    public function testGetAllConfig(): void
    {
        $allConfig = file_service_config();
        $this->assertIsArray($allConfig);
        $this->assertArrayHasKey('baseUrl', $allConfig);
        $this->assertArrayHasKey('http', $allConfig);
    }

    /**
     * 測試配置設定功能
     */
    public function testConfigSet(): void
    {
        // 測試單一配置設定
        file_service_config_set('test.key', 'test_value');
        $value = file_service_config('test.key');
        $this->assertEquals('test_value', $value);

        // 測試巢狀配置設定
        file_service_config_set('test.nested.key', 'nested_value');
        $nestedValue = file_service_config('test.nested.key');
        $this->assertEquals('nested_value', $nestedValue);
    }

    /**
     * 測試批量配置設定
     */
    public function testBatchConfigSet(): void
    {
        $config = [
            'batch.key1' => 'value1',
            'batch.key2' => 'value2',
            'batch.nested.key' => 'nested_value'
        ];

        file_service_config($config);

        $this->assertEquals('value1', file_service_config('batch.key1'));
        $this->assertEquals('value2', file_service_config('batch.key2'));
        $this->assertEquals('nested_value', file_service_config('batch.nested.key'));
    }

    /**
     * 測試 file_service_config_set 函數
     */
    public function testConfigSetFunction(): void
    {
        // 測試單一設定
        file_service_config_set('set.test', 'set_value');
        $this->assertEquals('set_value', file_service_config('set.test'));

        // 測試批量設定
        file_service_config_set([
            'set.batch1' => 'batch_value1',
            'set.batch2' => 'batch_value2'
        ]);

        $this->assertEquals('batch_value1', file_service_config('set.batch1'));
        $this->assertEquals('batch_value2', file_service_config('set.batch2'));
    }

    /**
     * 測試配置移除功能
     */
    public function testConfigForget(): void
    {
        // 設定配置
        file_service_config_set('forget.test', 'will_be_forgotten');
        $this->assertEquals('will_be_forgotten', file_service_config('forget.test'));

        // 移除配置
        file_service_config_forget('forget.test');
        $this->assertNull(file_service_config('forget.test'));
    }

    /**
     * 測試配置存在檢查
     */
    public function testConfigHas(): void
    {
        // 設定測試配置
        file_service_config_set('has.test', 'exists');

        // 測試存在的配置
        $this->assertTrue(file_service_config_has('has.test'));
        $this->assertTrue(file_service_config_has('baseUrl')); // 預設配置

        // 測試不存在的配置
        $this->assertFalse(file_service_config_has('non.existent'));
    }

    /**
     * 測試獲取所有配置
     */
    public function testConfigAll(): void
    {
        $allConfig = file_service_config_all();
        $this->assertIsArray($allConfig);
        $this->assertArrayHasKey('baseUrl', $allConfig);
    }

    /**
     * 測試參數驗證 - 無效的鍵型別
     */
    public function testInvalidKeyType(): void
    {
        $this->expectException(ConfigurationException::class);
        $this->expectExceptionCode(ConfigurationException::CONFIG_INVALID_PARAMETER);

        file_service_config(123); // 數字而非字串
    }

    /**
     * 測試參數驗證 - 空鍵
     */
    public function testEmptyKey(): void
    {
        $this->expectException(ConfigurationException::class);
        $this->expectExceptionCode(ConfigurationException::CONFIG_INVALID_PARAMETER);

        file_service_config_set('', 'value');
    }

    /**
     * 測試參數驗證 - 無效的鍵格式
     */
    public function testInvalidKeyFormat(): void
    {
        $this->expectException(ConfigurationException::class);
        $this->expectExceptionCode(ConfigurationException::CONFIG_INVALID_KEY);

        file_service_config_set('invalid..key', 'value'); // 雙點號
    }

    /**
     * 測試參數驗證 - 鍵包含無效字符
     */
    public function testInvalidKeyCharacters(): void
    {
        $this->expectException(ConfigurationException::class);
        $this->expectExceptionCode(ConfigurationException::CONFIG_INVALID_KEY);

        file_service_config_set('invalid@key', 'value'); // 包含 @ 符號
    }

    /**
     * 測試參數驗證 - 鍵以點號開始或結束
     */
    public function testKeyStartsOrEndsWithDot(): void
    {
        $this->expectException(ConfigurationException::class);
        $this->expectExceptionCode(ConfigurationException::CONFIG_INVALID_KEY);

        file_service_config_set('.invalid', 'value'); // 以點號開始
    }

    /**
     * 測試參數驗證 - 缺少值參數
     */
    public function testMissingValueParameter(): void
    {
        $this->expectException(ConfigurationException::class);
        $this->expectExceptionCode(ConfigurationException::CONFIG_MISSING_PARAMETER);

        file_service_config_set('test.key'); // 缺少值參數
    }

    /**
     * 測試參數驗證 - 空配置陣列
     */
    public function testEmptyConfigArray(): void
    {
        $this->expectException(ConfigurationException::class);
        $this->expectExceptionCode(ConfigurationException::CONFIG_INVALID_PARAMETER);

        file_service_config_set([]); // 空陣列
    }

    /**
     * 測試參數驗證 - 配置陣列包含非字串鍵
     */
    public function testConfigArrayWithNonStringKey(): void
    {
        $this->expectException(ConfigurationException::class);
        $this->expectExceptionCode(ConfigurationException::CONFIG_INVALID_PARAMETER);

        file_service_config_set([
            'valid.key' => 'value',
            123 => 'invalid_key' // 數字鍵
        ]);
    }

    /**
     * 測試點號語法的巢狀存取
     */
    public function testDotNotationAccess(): void
    {
        // 設定巢狀配置
        file_service_config_set('level1.level2.level3', 'deep_value');

        // 測試各層級存取
        $level1 = file_service_config('level1');
        $this->assertIsArray($level1);
        $this->assertArrayHasKey('level2', $level1);

        $level2 = file_service_config('level1.level2');
        $this->assertIsArray($level2);
        $this->assertArrayHasKey('level3', $level2);

        $level3 = file_service_config('level1.level2.level3');
        $this->assertEquals('deep_value', $level3);
    }

    /**
     * 測試配置覆蓋
     */
    public function testConfigOverride(): void
    {
        // 設定初始值
        file_service_config_set('override.test', 'original');
        $this->assertEquals('original', file_service_config('override.test'));

        // 覆蓋值
        file_service_config_set('override.test', 'overridden');
        $this->assertEquals('overridden', file_service_config('override.test'));
    }

    /**
     * 測試複雜的配置合併
     */
    public function testComplexConfigMerge(): void
    {
        // 設定基礎配置
        file_service_config([
            'complex.array' => ['a', 'b'],
            'complex.nested.key1' => 'value1'
        ]);

        // 合併更多配置
        file_service_config([
            'complex.array' => ['c', 'd'], // 這會覆蓋原陣列
            'complex.nested.key2' => 'value2'
        ]);

        $array = file_service_config('complex.array');
        $this->assertEquals(['c', 'd'], $array);

        $key1 = file_service_config('complex.nested.key1');
        $this->assertEquals('value1', $key1);

        $key2 = file_service_config('complex.nested.key2');
        $this->assertEquals('value2', $key2);
    }

    /**
     * 測試預設值的不同型別
     */
    public function testDefaultValueTypes(): void
    {
        // 測試不同型別的預設值
        $this->assertNull(file_service_config('non.existent', null));
        $this->assertFalse(file_service_config('non.existent', false));
        $this->assertEquals(0, file_service_config('non.existent', 0));
        $this->assertEquals('', file_service_config('non.existent', ''));
        $this->assertEquals([], file_service_config('non.existent', []));
        $this->assertEquals('custom', file_service_config('non.existent', 'custom'));
    }

    /**
     * 測試函數參數數量判斷
     */
    public function testFunctionArgumentCount(): void
    {
        // 測試獲取操作（一個參數）
        $value = file_service_config('baseUrl');
        $this->assertIsString($value);

        // 測試獲取操作（兩個參數，第二個是預設值）
        $value = file_service_config('non.existent', 'default');
        $this->assertEquals('default', $value);

        // 測試設定操作（使用明確的設定函數）
        file_service_config_set('test.set', 'actual_value');
        $this->assertEquals('actual_value', file_service_config('test.set'));
    }

    /**
     * 測試預設值功能
     */
    public function testDefaultValueHandling(): void
    {
        // 測試各種預設值
        $this->assertNull(file_service_config('non.existent', null));
        $this->assertFalse(file_service_config('non.existent', false));
        $this->assertEquals(0, file_service_config('non.existent', 0));
        $this->assertEquals('', file_service_config('non.existent', ''));
        $this->assertEquals([], file_service_config('non.existent', []));
        $this->assertEquals('custom', file_service_config('non.existent', 'custom'));

        // 測試設定操作（使用明確的設定函數）
        file_service_config_set('test.string', 'non_empty_string');
        $this->assertEquals('non_empty_string', file_service_config('test.string'));

        file_service_config_set('test.number', 123);
        $this->assertEquals(123, file_service_config('test.number'));

        file_service_config_set('test.true', true);
        $this->assertTrue(file_service_config('test.true'));
    }
}